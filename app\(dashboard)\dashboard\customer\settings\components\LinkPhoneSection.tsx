"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Phone } from "lucide-react";

interface LinkPhoneSectionProps {
  currentEmail?: string | null;
  currentPhone?: string | null;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkPhoneSection({
  currentPhone,
}: LinkPhoneSectionProps) {

  return (
    <Card className="border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black">
      <CardHeader className="pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-neutral-800 dark:text-neutral-100">
          <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
            <Phone className="w-4 h-4" />
          </div>
          Phone Number
        </CardTitle>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-2">
          {current<PERSON><PERSON>
            ? "Your current phone number linked to this account."
            : "No phone number is currently linked to your account."
          }
        </p>
      </CardHeader>
      <CardContent className="pt-4">
        {currentPhone ? (
          // Show current phone number (read-only)
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Current Phone Number
              </label>
              <div className="mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md">
                <span className="text-sm text-neutral-600 dark:text-neutral-400">{currentPhone}</span>
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                Phone number changes are not currently supported. Contact support if you need to update your number.
              </p>
            </div>
          </div>
        ) : (
          // No phone number linked
          <div className="text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700">
            <div className="p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit">
              <Phone className="w-6 h-6" />
            </div>
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2">
              No Phone Number
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto">
              No phone number is currently linked to your account. Phone number linking is not available at this time.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
