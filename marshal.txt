Read @c:\Users\<USER>\Desktop\dukancard/instructions\create-prd.mdc . I want to work on the frontend design and backend logic of settings page for both business and customer dashboard. First, I want to check that the backend is correct or not. I also want to fix messy codes and organize them better to be production ready and that every coder can work on the project quickly. Also check the logic to ensure the logic is correct and we are handling everything correctly and there are no edge cases. I want proper structuring and organization, so ensure we follow indsutry standard coding practices.

Then I want to enhance the visuals UI/UX of the page for both dsahboard and have similar style in both dashboards. I want the design to be responsive for all screen sizes, and you are free to re-design everything. Remove the glowing button logics to keep everything neat, modern and professional. Keep in mind that the design should be compatible for light and dark mode. You are free to restructure everything. 

Read @c:\Users\<USER>\OneDrive\Desktop\dukancard_fresh/instructions\create-prd.mdc @c:\Users\<USER>\OneDrive\Desktop\dukancard_fresh/instructions\generate-tasks.mdc @c:\Users\<USER>\OneDrive\Desktop\dukancard_fresh/tasks\prd-settings-refactor-enhancement.md @c:\Users\<USER>\OneDrive\Desktop\dukancard_fresh/tasks\tasks-settings-refactor-enhancement.md and modify the tasks to focus on frontend only now. I want you to redesign the settings page for both business and customer dashboard settings page. Ask me if you have any doubts before you modify tasks. I want mobile-first responsive design with touch-friendly interactions, but also make full use of screen space in desktop. Wait for my answers.