import { z } from "zod";

export const CustomerProfileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  // Email update will be handled separately via Supabase Auth flow
});

export const CustomerEmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email." }),
});

export const CustomerAddressSchema = z.object({
  address: z
    .string()
    .max(100, { message: "Address cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  pincode: z
    .string()
    .min(1, { message: "Pincode is required" })
    .regex(/^\d{6}$/, { message: "Must be a valid 6-digit pincode" }),
  city: z
    .string()
    .min(1, { message: "City is required" })
    .max(50, { message: "City cannot exceed 50 characters." })
    .refine((val) => val.trim().length > 0, { message: "City cannot be empty" }),
  state: z
    .string()
    .min(1, { message: "State is required" })
    .max(50, { message: "State cannot exceed 50 characters." })
    .refine((val) => val.trim().length > 0, { message: "State cannot be empty" }),
  locality: z
    .string()
    .min(1, { message: "Locality is required" })
    .refine((val) => val.trim().length > 0, { message: "Locality cannot be empty" }),
});

// Import and rename the reusable schema for clarity in this context
export const CustomerPasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" })
      .regex(/[A-Z]/, { message: "Password must contain at least one capital letter" })
      .regex(/[a-z]/, { message: "Password must contain at least one lowercase letter." })
      .regex(/[0-9]/, { message: "Password must contain at least one number" })
      .regex(/[^A-Za-z0-9]/, { message: "Password must contain at least one symbol" }),
    confirmPassword: z.string(),
    currentPassword: z.string().min(1, { message: "Current password is required." }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match.",
    path: ["confirmPassword"],
  });
