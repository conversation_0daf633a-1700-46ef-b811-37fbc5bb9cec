"use client";

import { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

import { ProductsProvider } from "./context/ProductsContext";
import { containerVariants } from "./types";
import { ProductWithVariantInfo } from "@/types/products";
import {
  ProductHeader,
  ProductFilters,
  ProductStats,
  ProductViewToggle,
  ProductTable,
  ProductGrid,
  ProductDeleteDialog,
  ProductLoadingState
} from "./components/product-ui";

interface ProductsClientProps {
  initialData: ProductWithVariantInfo[];
  initialCount: number;
  planLimit: number;
  error?: string;
}

export default function ProductsClient({
  initialData,
  initialCount,
  planLimit,
  error,
}: ProductsClientProps) {
  return (
    <ProductsProvider
      initialData={initialData}
      initialCount={initialCount}
      planLimit={planLimit}
      initialError={error}
    >
      <ProductsClientContent />
    </ProductsProvider>
  );
}

function ProductsClientContent() {
  const {
    viewType,
    isLoading,
    isInitialLoading,
    loadMoreProducts,
    currentPage,
    totalPages,
    products
  } = useProducts();

  // Intersection Observer for infinite scrolling
  useEffect(() => {
    if (currentPage >= totalPages) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoading) {
          loadMoreProducts();
        }
      },
      { threshold: 0.1 }
    );

    const loadMoreTrigger = document.getElementById("load-more-trigger");
    if (loadMoreTrigger) {
      observer.observe(loadMoreTrigger);
    }

    return () => {
      if (loadMoreTrigger) {
        observer.unobserve(loadMoreTrigger);
      }
    };
  }, [currentPage, totalPages, isLoading, loadMoreProducts]);

  return (
    <motion.div
      className="space-y-4 sm:space-y-5 md:space-y-6 px-0 sm:px-1 md:px-2 relative z-10"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Main Card Container */}
      <motion.div
        className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-2 sm:p-3 md:p-4 lg:p-6 mb-3 sm:mb-4 md:mb-5 transition-all duration-300 relative overflow-hidden"
      >

        <div className="relative z-10">

          {/* Product Header */}
          <ProductHeader />

          {/* Spacing */}
          <div className="h-4 sm:h-6 md:h-8"></div>

          {/* Stats Summary */}
          <motion.div
            className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-2 sm:p-3 md:p-4 lg:p-5 mb-3 sm:mb-4 md:mb-5 transition-all duration-300 relative overflow-hidden"
          >

            <ProductStats />
          </motion.div>

          {/* Action Bar */}
          <motion.div
            variants={containerVariants}
            className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-2 sm:p-3 md:p-4 lg:p-5 transition-all duration-300 relative overflow-hidden"
          >

            <div className="relative z-10">
              <ProductFilters />
            </div>
          </motion.div>

          {/* Count and View Toggle */}
          <motion.div
            className="rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md px-3 sm:px-4 md:px-5 py-2 sm:py-3 mb-2 sm:mb-2 md:mb-3 transition-all duration-300 relative overflow-hidden"
          >
            <ProductViewToggle />
          </motion.div>

          {/* Products List */}
          <div className="mt-4 sm:mt-6">
            {isInitialLoading ? (
              <ProductLoadingState view={viewType} />
            ) : (
              <AnimatePresence mode="wait">
                {viewType === "table" ? (
                  <ProductTable key="table" />
                ) : (
                  <ProductGrid key="grid" />
                )}
              </AnimatePresence>
            )}

            {/* Load More Trigger */}
            {products.length > 0 && currentPage < totalPages && (
              <div
                id="load-more-trigger"
                className="w-full h-20 flex items-center justify-center mt-4"
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadMoreProducts}
                  disabled={isLoading}
                  className="text-xs sm:text-sm"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <ProductDeleteDialog />
    </motion.div>
  );
}

// Import at the end to avoid circular dependencies
import { useProducts } from "./context/ProductsContext";
