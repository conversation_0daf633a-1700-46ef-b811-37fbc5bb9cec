/**
 * Scalable Storage Path Utilities
 *
 * This module provides utilities for generating scalable storage paths
 * that can handle billions of users efficiently using hash-based distribution.
 */

/**
 * Generate scalable user path using hash-based distribution
 *
 * @param userId - The user's UUID
 * @returns Scalable path: users/{prefix}/{midfix}/{userId}
 *
 * Example:
 * - Input: "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 * - Output: "users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890"
 */
export function getScalableUserPath(userId: string): string {
  if (!userId || typeof userId !== 'string') {
    throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);
  }

  if (userId.length < 4) {
    throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);
  }

  const prefix = userId.substring(0, 2).toLowerCase();
  const midfix = userId.substring(2, 4).toLowerCase();

  return `users/${prefix}/${midfix}/${userId}`;
}

/**
 * Generate profile image path
 */
export function getProfileImagePath(userId: string, timestamp: number): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/profile/logo_${timestamp}.webp`;
}

/**
 * Generate product image path (legacy - for backward compatibility)
 * @deprecated Use getProductBaseImagePath or getProductVariantImagePath instead
 */
export function getProductImagePath(
  userId: string,
  productId: string,
  imageIndex: number,
  timestamp: number
): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;
}

/**
 * Generate base product image path
 */
export function getProductBaseImagePath(
  userId: string,
  productId: string,
  imageIndex: number,
  timestamp: number
): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;
}

/**
 * Generate product variant image path
 */
export function getProductVariantImagePath(
  userId: string,
  productId: string,
  variantId: string,
  imageIndex: number,
  timestamp: number
): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;
}

/**
 * Generate gallery image path
 */
export function getGalleryImagePath(userId: string, timestamp: number): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/gallery/gallery_${timestamp}.webp`;
}

/**
 * Generate post image path
 */
export function getPostImagePath(
  userId: string,
  postId: string,
  imageIndex: number,
  timestamp: number,
  createdAt?: string
): string {
  const userPath = getScalableUserPath(userId);

  // Use post creation date if provided, otherwise use current date (for backward compatibility)
  const dateToUse = createdAt ? new Date(createdAt) : new Date();
  const year = dateToUse.getFullYear();
  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');

  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}

/**
 * Generate post folder path for deletion
 */
export function getPostFolderPath(userId: string, postId: string, createdAt: string): string {
  const userPath = getScalableUserPath(userId);
  const postDate = new Date(createdAt);
  const year = postDate.getFullYear();
  const month = String(postDate.getMonth() + 1).padStart(2, '0');

  return `${userPath}/posts/${year}/${month}/${postId}`;
}

/**
 * Generate customer avatar image path
 */
export function getCustomerAvatarPath(userId: string, timestamp: number): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/avatar/avatar_${timestamp}.webp`;
}

/**
 * Generate customer post image path
 */
export function getCustomerPostImagePath(
  userId: string,
  postId: string,
  imageIndex: number,
  timestamp: number,
  createdAt?: string
): string {
  const userPath = getScalableUserPath(userId);

  // Use post creation date if provided, otherwise use current date (for backward compatibility)
  const dateToUse = createdAt ? new Date(createdAt) : new Date();
  const year = dateToUse.getFullYear();
  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');

  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;
}

/**
 * Generate custom ad image path
 */
export function getCustomAdImagePath(userId: string, timestamp: number): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/ads/custom_ad_${timestamp}.webp`;
}

/**
 * Generate custom header image path
 */
export function getCustomHeaderImagePath(userId: string, timestamp: number): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/branding/header_${timestamp}.webp`;
}

/**
 * Generate theme-specific custom header image path
 */
export function getThemeSpecificHeaderImagePath(
  userId: string,
  timestamp: number,
  theme: 'light' | 'dark'
): string {
  const userPath = getScalableUserPath(userId);
  return `${userPath}/branding/header_${theme}_${timestamp}.webp`;
}

// Legacy utilities removed since migration is complete

/**
 * Path validation utilities
 */
export class PathValidator {
  /**
   * Validate if a path follows the new scalable structure
   */
  static isScalablePath(path: string): boolean {
    return path.startsWith('users/') && path.split('/').length >= 4;
  }

  /**
   * Extract user ID from scalable path
   */
  static extractUserIdFromPath(path: string): string | null {
    if (!this.isScalablePath(path)) {
      return null;
    }

    const parts = path.split('/');
    return parts[3]; // users/{prefix}/{midfix}/{userId}/...
  }

  /**
   * Validate path structure integrity
   */
  static validatePathStructure(userId: string, path: string): boolean {
    const expectedUserPath = getScalableUserPath(userId);
    return path.startsWith(expectedUserPath);
  }
}

/**
 * Storage analytics utilities
 */
export class StorageAnalytics {
  /**
   * Get storage distribution info for monitoring
   */
  static getDistributionInfo(userId: string): {
    prefix: string;
    midfix: string;
    bucket: string;
    estimatedPeers: number;
  } {
    const prefix = userId.substring(0, 2).toLowerCase();
    const midfix = userId.substring(2, 4).toLowerCase();

    // Estimate number of users in same bucket (assuming even distribution)
    const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets
    const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users

    return {
      prefix,
      midfix,
      bucket: `${prefix}/${midfix}`,
      estimatedPeers
    };
  }
}
