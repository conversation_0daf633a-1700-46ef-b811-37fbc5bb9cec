"use client";

import { Suspense } from "react";
import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import { ProductVariant } from "@/types/variants";
import { ProductWithVariants } from "@/types/products";
import ProductDetail from "../components/ProductDetail";
import ProductRecommendations from "../components/ProductRecommendations";
import { AdData } from "@/types/ad";

interface ProductDetailClientProps {
  product: ProductWithVariants;
  variants?: ProductVariant[];
  businessSlug: string;
  businessName: string;
  whatsappNumber: string | null;
  phoneNumber: string | null;
  businessProducts: ProductServiceData[];
  otherBusinessProducts: Array<ProductServiceData & { business_slug: string }>;
  topAdData: AdData;
  businessCustomAd?: {
    enabled?: boolean;
    image_url?: string;
    link_url?: string;
  } | null;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise";
}

export default function ProductDetailClient({
  product,
  variants = [],
  businessSlug,
  businessName,
  whatsappNumber,
  phoneNumber,
  businessProducts,
  otherBusinessProducts,
  topAdData,
  businessCustomAd,
  userPlan,
}: ProductDetailClientProps) {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading product details...</div>}>
      <div className="w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-8 min-h-screen max-w-7xl">
        <ProductDetail
          product={product}
          variants={variants}
          businessSlug={businessSlug}
          businessName={businessName}
          whatsappNumber={whatsappNumber}
          phoneNumber={phoneNumber}
          topAdData={topAdData}
          businessCustomAd={businessCustomAd}
          userPlan={userPlan}
        />

        {/* Product recommendations */}
        {(businessProducts.length > 0 || otherBusinessProducts.length > 0) && (
          <ProductRecommendations
            businessProducts={businessProducts.slice(0, 12)}
            otherBusinessProducts={otherBusinessProducts.slice(0, 12)}
            businessSlug={businessSlug}
          />
        )}
      </div>
    </Suspense>
  );
}
