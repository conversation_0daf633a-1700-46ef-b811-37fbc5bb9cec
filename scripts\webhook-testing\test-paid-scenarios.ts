#!/usr/bin/env tsx

/**
 * PAID SUBSCRIPTION SCENARIOS TEST
 * 
 * Tests all paid subscription scenarios including renewals, cancellations, etc.
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import { getScenariosByCategory } from './scenarios/subscriptionScenarios';
import { ScenarioTestRunner } from './runners/scenarioTestRunner';

const BUSINESS_ID = 'd4fe2395-3872-4522-9b67-0d280633f318';

async function testPaidScenarios() {
  console.log('💳 Paid Subscription Scenarios Test Suite');
  console.log('=========================================');
  console.log(`📋 Business ID: ${BUSINESS_ID}\n`);

  const paidScenarios = getScenariosByCategory('paid');
  console.log(`📊 Testing ${paidScenarios.length} paid subscription scenarios\n`);

  const runner = new ScenarioTestRunner(BUSINESS_ID);
  await runner.initialize();

  const results = [];
  
  for (const scenario of paidScenarios) {
    console.log(`\n💰 ${scenario.name}`);
    console.log(`   ${scenario.description}`);
    console.log('   ' + '─'.repeat(50));
    
    const result = await runner.runScenario(scenario);
    results.push(result);
    
    if (result.success) {
      console.log(`   ✅ PASSED (${result.duration.toFixed(2)}s)`);
    } else {
      console.log(`   ❌ FAILED (${result.duration.toFixed(2)}s)`);
      console.log(`   💬 ${result.message}`);
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Generate summary
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 PAID SCENARIOS SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${total - passed}`);
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All paid subscription scenarios working correctly!');
    console.log('✅ Active → Charged (renewal) flow validated');
    console.log('✅ Active → Cancelled → Free flow validated');
    console.log('✅ Active → Halted (payment failed) flow validated');
    console.log('✅ Active → Expired → Free flow validated');
    console.log('✅ Active → Completed → Free flow validated');
  } else {
    console.log('\n⚠️ Some paid scenarios failed');
    const failed = results.filter(r => !r.success);
    failed.forEach(result => {
      console.log(`❌ ${result.scenario.name}: ${result.message}`);
    });
  }
  
  console.log('='.repeat(60));
  
  process.exit(passed === total ? 0 : 1);
}

testPaidScenarios().catch(console.error);
