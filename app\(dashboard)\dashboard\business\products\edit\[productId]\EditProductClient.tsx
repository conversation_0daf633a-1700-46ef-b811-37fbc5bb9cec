"use client";

import { useTransition } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { Package, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import StandaloneProductForm, { ProductFormValues } from "../../components/StandaloneProductForm";
import { ProductServiceData, updateProductService, addProductVariant, updateProductVariant, deleteProductVariant } from "../../actions";
import { ProductVariant } from "@/types/variants";

interface EditProductClientProps {
  product: ProductServiceData;
  variants?: ProductVariant[];
  planLimit?: number;
  currentAvailableCount?: number;
}

export default function EditProductClient({ product, variants, planLimit, currentAvailableCount }: EditProductClientProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Track original variants to detect deletions
  const originalVariantIds = variants?.map(v => v.id) || [];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.4
      }
    }
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.1,
        duration: 0.4,
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    }
  };

  const handleSubmit = async (
    values: ProductFormValues & { variants?: ProductVariant[] },
    imageFiles?: (File | null)[],
    featuredImageIndex?: number,
    removedImageIndices?: number[]
  ) => {
    startTransition(async () => {
      const formData = new FormData();

      // Add form values
      Object.entries(values).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          if (typeof value === "boolean") {
            formData.append(key, value.toString());
          } else {
            formData.append(key, String(value));
          }
        }
      });

      // Add featured image index
      if (featuredImageIndex !== undefined) {
        formData.append("featuredImageIndex", String(featuredImageIndex));
      }

      // Add removed image indices
      if (removedImageIndices && removedImageIndices.length > 0) {
        formData.append("removedImageIndices", JSON.stringify(removedImageIndices));
      }

      // Add image files
      if (imageFiles && imageFiles.length > 0) {
        imageFiles.forEach((file, index) => {
          if (file) {
            formData.append(`productImage_${index}`, file);
          }
        });
      }

      try {
        const result = await updateProductService(product.id!, formData);

        if (result.success && result.data) {
          // Handle variant updates if variants were provided
          if (values.variants) {
            toast.success("Product updated! Processing variants...");

            let variantSuccessCount = 0;
            let variantErrorCount = 0;

            // First, handle variant deletions
            const currentVariantIds = values.variants.map(v => v.id);
            const deletedVariantIds = originalVariantIds.filter(id =>
              !currentVariantIds.includes(id) && !id.startsWith('temp-')
            );

            for (const deletedId of deletedVariantIds) {
              try {
                const deleteResult = await deleteProductVariant(deletedId);
                if (deleteResult.success) {
                  variantSuccessCount++;
                } else {
                  variantErrorCount++;
                  console.error('Failed to delete variant:', deletedId, deleteResult.error);
                }
              } catch (deleteError) {
                variantErrorCount++;
                console.error('Error deleting variant:', deletedId, deleteError);
              }
            }

            // Process each variant (create/update)
            for (const variant of values.variants) {
              try {
                console.log('Processing variant:', variant.variant_name, 'Removed indices:', variant._removedImageIndices);

                const variantFormData = new FormData();
                variantFormData.append('product_id', product.id!);
                variantFormData.append('variant_name', variant.variant_name);
                variantFormData.append('variant_values', JSON.stringify(variant.variant_values));

                // Handle pricing - ensure proper format
                if (variant.base_price !== undefined && variant.base_price !== null && variant.base_price > 0) {
                  variantFormData.append('base_price', variant.base_price.toString());
                }
                if (variant.discounted_price !== undefined && variant.discounted_price !== null && variant.discounted_price > 0) {
                  variantFormData.append('discounted_price', variant.discounted_price.toString());
                }

                variantFormData.append('is_available', variant.is_available ? 'true' : 'false');
                variantFormData.append('featured_image_index', variant.featured_image_index.toString());

                // Add removed image indices if they exist
                if (variant._removedImageIndices && variant._removedImageIndices.length > 0) {
                  console.log('Adding removed image indices to FormData:', variant._removedImageIndices);
                  variantFormData.append('removedImageIndices', JSON.stringify(variant._removedImageIndices));
                }

                // Add variant images if they exist
                if (variant._imageFiles && variant._imageFiles.length > 0) {
                  variant._imageFiles.forEach((file, index) => {
                    if (file) {
                      variantFormData.append(`images[${index}]`, file);
                    }
                  });
                }

                // Check if this is a new variant (temp ID) or existing variant
                if (variant.id.startsWith('temp-')) {
                  // Create new variant
                  const variantResult = await addProductVariant(variantFormData);
                  if (variantResult.success) {
                    variantSuccessCount++;
                  } else {
                    variantErrorCount++;
                    console.error('Failed to create variant:', variant.variant_name, variantResult.error);
                  }
                } else {
                  // Update existing variant
                  const variantResult = await updateProductVariant(variant.id, variantFormData);
                  if (variantResult.success) {
                    variantSuccessCount++;
                  } else {
                    variantErrorCount++;
                    console.error('Failed to update variant:', variant.variant_name, variantResult.error);
                  }
                }
              } catch (variantError) {
                variantErrorCount++;
                console.error('Error processing variant:', variant.variant_name, variantError);
              }
            }

            if (variantSuccessCount > 0 && variantErrorCount === 0) {
              toast.success(`Product and all variants updated successfully!`);
            } else if (variantSuccessCount > 0) {
              toast.warning(`Product updated! ${variantSuccessCount} variant${variantSuccessCount > 1 ? 's' : ''} processed successfully, ${variantErrorCount} failed.`);
            } else if (variantErrorCount > 0) {
              toast.warning(`Product updated, but all ${variantErrorCount} variant${variantErrorCount > 1 ? 's' : ''} failed to process.`);
            }
          } else {
            toast.success("Product updated successfully!");
          }

          router.push("/dashboard/business/products");
        } else {
          const errorMessage = result.error || "Failed to update product";

          // Show user-friendly error message
          if (errorMessage.includes("Image exceeds 15MB limit")) {
            toast.error("Image too large", {
              description: "Please select images smaller than 15MB each"
            });
          } else if (errorMessage.includes("Invalid file type")) {
            toast.error("Invalid file type", {
              description: "Please select JPG, PNG, WebP, or GIF images"
            });
          } else if (errorMessage.includes("Body exceeded")) {
            toast.error("Upload size limit exceeded", {
              description: "Please try uploading fewer images or smaller file sizes"
            });
          } else {
            toast.error("Failed to update product", {
              description: errorMessage
            });
          }
        }
      } catch (error) {
        console.error("Error updating product:", error);
        toast.error("An unexpected error occurred");
      }
    });
  };

  return (
    <motion.div
      className="w-full max-w-5xl mx-auto px-4 sm:px-6 py-6 sm:py-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Back button */}
      <Button
        variant="ghost"
        size="sm"
        className="mb-4 text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200"
        onClick={() => router.push('/dashboard/business/products')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Products
      </Button>

      {/* Header */}
      <motion.div
        className="mb-6 sm:mb-8"
        variants={headerVariants}
      >
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-xl bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/20 dark:to-blue-800/10 text-blue-600 dark:text-blue-400">
            <Package className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-neutral-800 dark:text-neutral-100">
              Edit Product
            </h1>
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
              Update details for: {product.name}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Form */}
      <StandaloneProductForm
        initialData={product}
        initialVariants={variants}
        onSubmit={handleSubmit}
        isSubmitting={isPending}
        isEditing={true}
        planLimit={planLimit}
        currentAvailableCount={currentAvailableCount}
      />
    </motion.div>
  );
}
