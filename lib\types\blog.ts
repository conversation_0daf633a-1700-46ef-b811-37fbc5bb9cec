/**
 * Blog-related TypeScript interfaces and types
 */

export interface Blog {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image_url?: string;
  author_name: string;
  author_email?: string;
  status: BlogStatus;
  meta_title?: string;
  meta_description?: string;
  categories: string[];
  tags: string[];
  reading_time_minutes?: number;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export type BlogStatus = 'draft' | 'published' | 'archived';

export interface BlogListItem {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  featured_image_url?: string;
  author_name: string;
  categories: string[];
  tags: string[];
  reading_time_minutes?: number;
  published_at?: string;
}

export interface BlogSearchParams {
  query?: string;
  sort?: 'newest' | 'oldest';
  page?: number;
  limit?: number;
}

export interface BlogSearchResult {
  blogs: BlogListItem[];
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  limit: number;
}

export interface BlogMetadata {
  title: string;
  description: string;
  image?: string;
  url: string;
  publishedTime?: string;
  author: string;
  tags?: string[];
  categories?: string[];
}

export interface CreateBlogData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image_url?: string;
  author_name?: string;
  author_email?: string;
  status?: BlogStatus;
  meta_title?: string;
  meta_description?: string;
  categories?: string[];
  tags?: string[];
  published_at?: string;
}

export interface UpdateBlogData extends Partial<CreateBlogData> {
  id: string;
}
