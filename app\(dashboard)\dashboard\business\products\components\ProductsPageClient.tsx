"use client";

import { motion } from "framer-motion";

import ProductsClient from "../ProductsClient";

import { ProductWithVariantInfo } from "@/types/products";

interface ProductsPageClientProps {
  initialData: ProductWithVariantInfo[];
  initialCount: number;
  planLimit: number;
  error?: string;
}

export default function ProductsPageClient({
  initialData,
  initialCount,
  planLimit,
  error,
}: ProductsPageClientProps) {
  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6 relative"
    >
      <motion.div variants={itemVariants} className="mb-6 relative z-10">
        {/* Products Client Component - Direct without wrapper */}
        <ProductsClient
          initialData={initialData}
          initialCount={initialCount}
          planLimit={planLimit}
          error={error}
        />
      </motion.div>
    </motion.div>
  );
}
