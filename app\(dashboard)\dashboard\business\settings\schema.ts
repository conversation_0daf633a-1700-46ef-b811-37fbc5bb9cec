import { z } from "zod";

// Schema for updating email
export const EmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
});

// Schema for linking email
export const LinkEmailSchema = z.object({
  email: z.string().email('Invalid email address.'),
});

// Schema for verifying email OTP
export const VerifyEmailOTPSchema = z.object({
  email: z.string().email('Invalid email address.'),
  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),
});

// Import the password complexity schema
import { PasswordComplexitySchema } from "@/lib/schemas/authSchemas";

// Create a schema for password update with current password verification
export const PasswordSchema = z.object({
  currentPassword: z.string().min(1, { message: "Current password is required." }),
  newPassword: PasswordComplexitySchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match.",
  path: ["confirmPassword"],
});
