"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ChevronRight,
  Edit,
  Trash2,
  Package,
  Eye,
  EyeOff,
  Plus,
  MoreHorizontal,
  Settings
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { ProductVariant } from "@/types/variants";
import { variantDisplay } from "@/lib/utils/variantHelpers";
import { cn } from "@/lib/utils";

import BulkVariantOperations from "./BulkVariantOperations";
import { bulkUpdateVariants, BulkVariantOperation } from "../actions/bulkVariantOperations";
import { toast } from "sonner";

interface VariantTableProps {
  productId: string;
  variants: ProductVariant[];
  isLoading?: boolean;
  onAddVariant?: () => void;
  onEditVariant?: (_variant: ProductVariant) => void;
  onDeleteVariant?: (_variantId: string) => void;
  onToggleVariantAvailability?: (_variantId: string, _isAvailable: boolean) => void;
  className?: string;
}

export default function VariantTable({
  variants,
  onAddVariant,
  onEditVariant,
  onDeleteVariant,
  onToggleVariantAvailability,
  className,
}: VariantTableProps) {
  const [expandedVariants, setExpandedVariants] = useState<Set<string>>(new Set());
  const [selectedVariantIds, setSelectedVariantIds] = useState<string[]>([]);
  const [showBulkOperations, setShowBulkOperations] = useState(false);

  const toggleVariantExpansion = (variantId: string) => {
    const newExpanded = new Set(expandedVariants);
    if (newExpanded.has(variantId)) {
      newExpanded.delete(variantId);
    } else {
      newExpanded.add(variantId);
    }
    setExpandedVariants(newExpanded);
  };

  const formatPrice = (price: number | null | undefined) => {
    if (!price) return "-";
    return price.toLocaleString("en-IN", {
      style: "currency",
      currency: "INR",
    });
  };

  const getVariantStatusBadge = (isAvailable: boolean) => {
    return (
      <Badge
        variant={isAvailable ? "default" : "secondary"}
        className={cn(
          "text-xs font-medium",
          isAvailable
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
        )}
      >
        {isAvailable ? "Available" : "Unavailable"}
      </Badge>
    );
  };

  const handleBulkUpdate = async (variantIds: string[], operation: BulkVariantOperation, value?: number) => {
    try {
      const result = await bulkUpdateVariants(variantIds, operation, value);

      if (result.success) {
        toast.success(result.message);
        // Clear selection after successful operation
        setSelectedVariantIds([]);

        // Trigger a refresh of the variants data
        // This would typically be handled by the parent component
        // For now, we'll just show the success message
      } else {
        toast.error(result.message);
        if (result.errors && result.errors.length > 0) {
          result.errors.forEach(error => toast.error(error));
        }
      }
    } catch (error) {
      console.error("Bulk update error:", error);
      toast.error("Failed to perform bulk operation. Please try again.");
    }
  };

  if (variants.length === 0) {
    return (
      <div className={cn("rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900", className)}>
        <div className="p-8 text-center">
          <Package className="mx-auto h-12 w-12 text-neutral-400 dark:text-neutral-600 mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
            No variants yet
          </h3>
          <p className="text-neutral-500 dark:text-neutral-400 mb-4">
            Create variants to offer different options for this product.
          </p>
          {onAddVariant && (
            <Button
              type="button"
              onClick={onAddVariant}
              className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add First Variant
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("rounded-lg border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900 overflow-hidden", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5 text-neutral-600 dark:text-neutral-400" />
          <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100">
            Product Variants
          </h3>
          <Badge variant="secondary" className="text-xs">
            {variants.length}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          {variants.length > 1 && (
            <Button
              type="button"
              onClick={() => setShowBulkOperations(!showBulkOperations)}
              variant="outline"
              size="sm"
            >
              <Settings className="mr-2 h-4 w-4" />
              {showBulkOperations ? "Hide" : "Bulk Operations"}
            </Button>
          )}
          {onAddVariant && (
            <Button
              type="button"
              onClick={onAddVariant}
              size="sm"
              className="bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Variant
            </Button>
          )}
        </div>
      </div>

      {/* Bulk Operations */}
      {showBulkOperations && variants.length > 1 && (
        <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 bg-white dark:bg-black">
          <BulkVariantOperations
            variants={variants}
            selectedVariantIds={selectedVariantIds}
            onSelectionChange={setSelectedVariantIds}
            onBulkUpdate={handleBulkUpdate}
          />
        </div>
      )}

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-neutral-50 dark:hover:bg-neutral-900">
            <TableHead className="w-[40px]"></TableHead>
            <TableHead className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              Variant
            </TableHead>
            <TableHead className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              Properties
            </TableHead>
            <TableHead className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              Base Price
            </TableHead>
            <TableHead className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              Sale Price
            </TableHead>
            <TableHead className="text-center text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              Status
            </TableHead>
            <TableHead className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {variants.map((variant) => {
            const isExpanded = expandedVariants.has(variant.id);
            const displayPrice = variantDisplay.getDisplayPrice(variant);
            const hasDiscount = variantDisplay.hasDiscount(variant);
            const hasImages = variant.images && variant.images.length > 0;

            return (
              <React.Fragment key={variant.id}>
                {/* Main Variant Row */}
                <motion.tr
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="hover:bg-neutral-50 dark:hover:bg-neutral-900 transition-colors"
                >
                  {/* Expand/Collapse Button */}
                  <TableCell className="p-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleVariantExpansion(variant.id)}
                      className="h-6 w-6 p-0"
                    >
                      <motion.div
                        animate={{ rotate: isExpanded ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </motion.div>
                    </Button>
                  </TableCell>

                  {/* Variant Name */}
                  <TableCell className="font-medium text-xs sm:text-sm text-neutral-800 dark:text-neutral-100">
                    <div className="max-w-xs truncate">{variant.variant_name}</div>
                  </TableCell>

                  {/* Variant Properties */}
                  <TableCell className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
                    <div className="flex flex-wrap gap-1">
                      {Object.entries(variant.variant_values).map(([type, value]) => (
                        <Badge key={`${type}-${value}`} variant="outline" className="text-xs">
                          {type}: {value}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>

                  {/* Base Price */}
                  <TableCell className="text-right text-xs sm:text-sm text-neutral-600 dark:text-neutral-400">
                    {formatPrice(variant.base_price)}
                  </TableCell>

                  {/* Sale Price */}
                  <TableCell className="text-right text-xs sm:text-sm">
                    {hasDiscount ? (
                      <div className="flex flex-col items-end">
                        <span className="text-green-600 dark:text-green-400 font-medium">
                          {formatPrice(variant.discounted_price)}
                        </span>
                        <span className="text-xs text-neutral-500 line-through">
                          {formatPrice(variant.base_price)}
                        </span>
                      </div>
                    ) : (
                      <span className="text-neutral-600 dark:text-neutral-400">
                        {formatPrice(displayPrice)}
                      </span>
                    )}
                  </TableCell>

                  {/* Status */}
                  <TableCell className="text-center">
                    {getVariantStatusBadge(variant.is_available)}
                  </TableCell>

                  {/* Actions */}
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEditVariant && (
                          <DropdownMenuItem onClick={() => onEditVariant(variant)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {onToggleVariantAvailability && (
                          <DropdownMenuItem
                            onClick={() => onToggleVariantAvailability(variant.id, !variant.is_available)}
                          >
                            {variant.is_available ? (
                              <>
                                <EyeOff className="mr-2 h-4 w-4" />
                                Make Unavailable
                              </>
                            ) : (
                              <>
                                <Eye className="mr-2 h-4 w-4" />
                                Make Available
                              </>
                            )}
                          </DropdownMenuItem>
                        )}
                        {onDeleteVariant && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => onDeleteVariant(variant.id)}
                              className="text-red-600 dark:text-red-400"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </motion.tr>

                {/* Expanded Variant Details Row - Rendered inline */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.tr
                      key={`expanded-${variant.id}`}
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="bg-neutral-50 dark:bg-neutral-900"
                    >
                      <TableCell colSpan={7} className="p-0">
                        <div className="p-6">
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Variant Details */}
                            <div className="space-y-4">
                              <h4 className="text-lg font-medium text-neutral-900 dark:text-neutral-100">
                                Variant Details
                              </h4>

                              <div className="space-y-3">
                                <div>
                                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                    Variant Name
                                  </label>
                                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                    {variant.variant_name}
                                  </p>
                                </div>

                                <div>
                                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                    Properties
                                  </label>
                                  <div className="flex flex-wrap gap-2 mt-1">
                                    {Object.entries(variant.variant_values).map(([type, value]) => (
                                      <Badge key={`${type}-${value}`} variant="outline" className="text-xs">
                                        <span className="font-medium">{type}:</span> {value}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                      Base Price
                                    </label>
                                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                      {formatPrice(variant.base_price)}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                      Sale Price
                                    </label>
                                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                      {formatPrice(variant.discounted_price)}
                                    </p>
                                  </div>
                                </div>

                                <div>
                                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                    Availability
                                  </label>
                                  <div className="mt-1">
                                    {getVariantStatusBadge(variant.is_available)}
                                  </div>
                                </div>

                                <div>
                                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                    Created
                                  </label>
                                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                    {new Date(variant.created_at).toLocaleDateString("en-IN", {
                                      year: "numeric",
                                      month: "long",
                                      day: "numeric",
                                    })}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Variant Images */}
                            <div className="space-y-4">
                              <h4 className="text-lg font-medium text-neutral-900 dark:text-neutral-100">
                                Variant Images
                              </h4>

                              {hasImages ? (
                                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                                  {variant.images.map((imageUrl, index) => (
                                    <div
                                      key={index}
                                      className={cn(
                                        "relative aspect-square rounded-lg overflow-hidden border-2",
                                        index === variant.featured_image_index
                                          ? "border-[var(--brand-gold)]"
                                          : "border-neutral-200 dark:border-neutral-700"
                                      )}
                                    >
                                      <Image
                                        src={imageUrl}
                                        alt={`${variant.variant_name} - Image ${index + 1}`}
                                        fill
                                        className="object-cover"
                                      />
                                      {index === variant.featured_image_index && (
                                        <div className="absolute top-2 left-2">
                                          <Badge className="bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] text-xs">
                                            Featured
                                          </Badge>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-center py-8 border-2 border-dashed border-neutral-200 dark:border-neutral-700 rounded-lg">
                                  <Package className="mx-auto h-8 w-8 text-neutral-400 dark:text-neutral-600 mb-2" />
                                  <p className="text-sm text-neutral-500 dark:text-neutral-400">
                                    No images for this variant
                                  </p>
                                  <p className="text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                                    Product images will be used as fallback
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex gap-3 mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-700">
                            {onEditVariant && (
                              <Button
                                type="button"
                                onClick={() => onEditVariant(variant)}
                                variant="outline"
                                size="sm"
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Variant
                              </Button>
                            )}
                            {onToggleVariantAvailability && (
                              <Button
                                onClick={() => onToggleVariantAvailability(variant.id, !variant.is_available)}
                                variant="outline"
                                size="sm"
                              >
                                {variant.is_available ? (
                                  <>
                                    <EyeOff className="mr-2 h-4 w-4" />
                                    Make Unavailable
                                  </>
                                ) : (
                                  <>
                                    <Eye className="mr-2 h-4 w-4" />
                                    Make Available
                                  </>
                                )}
                              </Button>
                            )}
                            {onDeleteVariant && (
                              <Button
                                onClick={() => onDeleteVariant(variant.id)}
                                variant="outline"
                                size="sm"
                                className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Variant
                              </Button>
                            )}
                          </div>
                        </div>
                      </TableCell>
                    </motion.tr>
                  )}
                </AnimatePresence>
              </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
