'use client';

import React, { useEffect } from 'react';
import { useActionState } from 'react';  // Updated from useFormState
import { useFormStatus } from 'react-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { updateCustomerPassword, type UpdatePasswordFormState } from './actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { PasswordComplexitySchema } from '@/lib/schemas/authSchemas';

// Client-side schema
const UpdatePasswordFormSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required.'),
  newPassword: PasswordComplexitySchema,
});

type UpdatePasswordFormData = z.infer<typeof UpdatePasswordFormSchema>;

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button type="submit" disabled={pending} aria-disabled={pending}>
      {pending ? 'Updating...' : 'Update Password'}
    </Button>
  );
}

export function UpdatePasswordForm() {
  const initialState: UpdatePasswordFormState = { message: null, errors: {}, success: false };
  const [state, dispatch] = useActionState(updateCustomerPassword, initialState);

  const form = useForm<UpdatePasswordFormData>({
    resolver: zodResolver(UpdatePasswordFormSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (state.success) {
      toast.success(state.message || 'Password updated successfully!');
      form.reset(); // Reset form on success
    } else if (state.message && !state.success) {
      // Show general errors
      if (!state.errors || Object.keys(state.errors).length === 0) {
         toast.error(state.message);
      }
      // Set server-side field errors into react-hook-form state
      if (state.errors?.currentPassword) {
        form.setError('currentPassword', { type: 'server', message: state.errors.currentPassword.join(', ') });
      }
       if (state.errors?.newPassword) {
         form.setError('newPassword', { type: 'server', message: state.errors.newPassword.join(', ') });
       }
    }
  }, [state, form]);

  return (
    <form action={dispatch} className="space-y-4">
      {/* Display non-field specific errors */}
      {state.message && !state.success && (!state.errors || Object.keys(state.errors).length === 0) && (
         <p className="text-sm font-medium text-destructive">{state.message}</p>
       )}

      <div className="space-y-2">
        <Label htmlFor="currentPassword">Current Password</Label>
        <Input
          id="currentPassword"
          type="password"
          {...form.register('currentPassword')}
          aria-invalid={!!form.formState.errors.currentPassword}
          aria-describedby="currentPassword-error"
        />
        {/* Combined client/server validation error */}
        {form.formState.errors.currentPassword && (
          <p id="currentPassword-error" className="text-sm font-medium text-destructive">
            {form.formState.errors.currentPassword.message}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="newPassword">New Password</Label>
        <Input
          id="newPassword"
          type="password"
          {...form.register('newPassword')}
          aria-invalid={!!form.formState.errors.newPassword}
          aria-describedby="newPassword-error"
        />
        <p className="text-xs text-muted-foreground">
          Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol
        </p>
         {/* Combined client/server validation error */}
        {form.formState.errors.newPassword && (
          <p id="newPassword-error" className="text-sm font-medium text-destructive">
            {form.formState.errors.newPassword.message}
          </p>
        )}
      </div>

      <SubmitButton />
    </form>
  );
}
