import { PaginationInfo } from "@/lib/types/blog";

const DEFAULT_LIMIT = 12;

/**
 * Get pagination info for current search
 */
export function getPaginationInfo(
  currentPage: number,
  totalCount: number,
  limit: number = DEFAULT_LIMIT
): PaginationInfo {
  const totalPages = Math.ceil(totalCount / limit);
  
  return {
    currentPage,
    totalPages,
    totalCount,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
    limit,
  };
}

/**
 * Calculate the range of items being displayed
 */
export function getDisplayRange(
  currentPage: number,
  totalCount: number,
  limit: number = DEFAULT_LIMIT
): { start: number; end: number } {
  const start = (currentPage - 1) * limit + 1;
  const end = Math.min(currentPage * limit, totalCount);
  
  return { start, end };
}

/**
 * Generate page numbers for pagination display
 */
export function generatePageNumbers(
  currentPage: number,
  totalPages: number,
  maxVisiblePages: number = 7
): (number | 'ellipsis')[] {
  const pages: (number | 'ellipsis')[] = [];

  if (totalPages <= maxVisiblePages) {
    // Show all pages if total pages is small
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (currentPage > 4) {
      pages.push('ellipsis');
    }

    // Show pages around current page
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    if (currentPage < totalPages - 3) {
      pages.push('ellipsis');
    }

    // Always show last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
  }

  return pages;
}
