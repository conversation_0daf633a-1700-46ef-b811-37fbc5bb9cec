"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, X, Filter, Calendar, Tag } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { BlogSearchParams } from "@/lib/types/blog";
import { useDebounce } from "@/lib/utils/debounce";

interface BlogSearchProps {
  onSearch: (_params: BlogSearchParams) => void;
  tags: string[];
  loading?: boolean;
  initialParams?: BlogSearchParams;
}

export default function BlogSearch({
  onSearch,
  tags,
  loading = false,
  initialParams = {},
}: BlogSearchProps) {
  const [searchQuery, setSearchQuery] = useState(initialParams.query || "");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<"newest" | "oldest">(initialParams.sort || "newest");
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Effect to trigger search when parameters change
  useEffect(() => {
    const params: BlogSearchParams = {
      query: debouncedSearchQuery || undefined,
      sort: sortBy,
    };

    onSearch(params);
  }, [debouncedSearchQuery, selectedTags, sortBy, onSearch]);

  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedTags([]);
    setSortBy("newest");
    setShowAdvancedFilters(false);
  }, []);

  const handleTagToggle = useCallback((tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  }, []);

  const activeFiltersCount = [
    searchQuery.trim() !== "",
    selectedTags.length > 0,
    sortBy !== "newest"
  ].filter(Boolean).length;

  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        {/* Main Search Row */}
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search blog posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10"
              disabled={loading}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery("")}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Quick Filters */}
          <div className="flex gap-2">
            {/* Sort Filter */}
            <Select value={sortBy} onValueChange={(value: "newest" | "oldest") => setSortBy(value)} disabled={loading}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="oldest">Oldest</SelectItem>
              </SelectContent>
            </Select>

            {/* Advanced Filters Toggle */}
            <Popover open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="relative">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                  {activeFiltersCount > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
                    >
                      {activeFiltersCount}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="end">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold">Advanced Filters</h4>
                    {activeFiltersCount > 0 && (
                      <Button variant="ghost" size="sm" onClick={handleClearFilters}>
                        Clear All
                      </Button>
                    )}
                  </div>

                  <Separator />

                  {/* Tags Filter */}
                  {tags.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium flex items-center mb-3">
                        <Tag className="h-4 w-4 mr-2" />
                        Tags
                      </Label>
                      <div className="max-h-32 overflow-y-auto space-y-2">
                        {tags.slice(0, 10).map((tag) => (
                          <div key={tag} className="flex items-center space-x-2">
                            <Checkbox
                              id={`tag-${tag}`}
                              checked={selectedTags.includes(tag)}
                              onCheckedChange={() => handleTagToggle(tag)}
                            />
                            <Label 
                              htmlFor={`tag-${tag}`} 
                              className="text-sm cursor-pointer flex-1"
                            >
                              {tag}
                            </Label>
                          </div>
                        ))}
                        {tags.length > 10 && (
                          <p className="text-xs text-muted-foreground">
                            +{tags.length - 10} more tags available
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Active Filters Display */}
        <AnimatePresence>
          {activeFiltersCount > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="flex flex-wrap gap-2 pt-4 border-t border-border"
            >
              {searchQuery.trim() && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Search className="h-3 w-3" />
                  {searchQuery.trim()}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchQuery("")}
                    className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}

              {selectedTags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  <Tag className="h-3 w-3" />
                  {tag}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleTagToggle(tag)}
                    className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}

              {sortBy !== "newest" && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {sortBy === "oldest" ? "Oldest First" : "Newest First"}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSortBy("newest")}
                    className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear all filters
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Loading State */}
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center justify-center py-4"
          >
            <div className="flex items-center text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
              Searching...
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
