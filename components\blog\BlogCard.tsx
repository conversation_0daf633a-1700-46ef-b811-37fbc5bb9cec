"use client";

import { motion } from "framer-motion";
import { Calendar, Clock, User, ArrowRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { BlogListItem } from "@/lib/types/blog";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";

interface BlogCardProps {
  blog: BlogListItem;
}

export default function BlogCard({ blog }: BlogCardProps) {
  const publishedDate = blog.published_at ? new Date(blog.published_at) : null;
  const formattedDate = publishedDate ? format(publishedDate, "MMM dd, yyyy") : null;

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className="h-full"
    >
      <Card className="h-full overflow-hidden border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-card/50 backdrop-blur-sm">
        <Link href={`/blog/${blog.slug}`} className="block h-full">
          {/* Featured Image */}
          <div className="relative h-48 overflow-hidden">
            {blog.featured_image_url ? (
              <Image
                src={blog.featured_image_url}
                alt={blog.title}
                fill
                className="object-cover transition-transform duration-300 hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center">
                <div className="text-6xl opacity-20">📝</div>
              </div>
            )}
            
            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            
            {/* Categories */}
            {blog.categories.length > 0 && (
              <div className="absolute top-4 left-4">
                <Badge 
                  variant="secondary" 
                  className="bg-background/90 backdrop-blur-sm text-foreground border-0"
                >
                  {blog.categories[0]}
                </Badge>
              </div>
            )}
          </div>

          <CardContent className="p-6 flex flex-col h-[calc(100%-12rem)]">
            {/* Title */}
            <h3 className="text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary transition-colors">
              {blog.title}
            </h3>

            {/* Excerpt */}
            {blog.excerpt && (
              <p className="text-muted-foreground mb-4 line-clamp-3 flex-grow">
                {blog.excerpt}
              </p>
            )}

            {/* Tags */}
            {blog.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {blog.tags.slice(0, 3).map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="text-xs border-primary/20 text-primary/80 hover:bg-primary/10"
                  >
                    {tag}
                  </Badge>
                ))}
                {blog.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{blog.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* Meta Information */}
            <div className="flex items-center justify-between text-sm text-muted-foreground mt-auto pt-4 border-t border-border/50">
              <div className="flex items-center space-x-4">
                {/* Author */}
                <div className="flex items-center">
                  <User className="h-3 w-3 mr-1" />
                  <span className="truncate max-w-24">{blog.author_name}</span>
                </div>

                {/* Reading Time */}
                {blog.reading_time_minutes && (
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{blog.reading_time_minutes} min</span>
                  </div>
                )}
              </div>

              {/* Published Date */}
              {formattedDate && (
                <div className="flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>{formattedDate}</span>
                </div>
              )}
            </div>

            {/* Read More Button */}
            <motion.div
              className="mt-4"
              whileHover={{ x: 4 }}
              transition={{ duration: 0.2 }}
            >
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full justify-between group hover:bg-primary/10 hover:text-primary"
              >
                <span>Read More</span>
                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </motion.div>
          </CardContent>
        </Link>
      </Card>
    </motion.div>
  );
}
