import { Metadata } from "next";
import { Suspense } from "react";
import Blog<PERSON>istingClient from "./components/BlogListingClient";
import { searchBlogs } from "@/lib/actions/blogs";
import BlogListingSkeleton from "@/components/blog/BlogListingSkeleton";

export const metadata: Metadata = {
  title: "Blogs",
  description: "Discover the latest insights, tips, and updates about digital business cards, networking, and business growth from the DukanCard team.",
  keywords: ["digital business cards", "networking", "business growth", "entrepreneurship", "technology"],
  openGraph: {
    title: "Blog | DukanCard",
    description: "Discover the latest insights, tips, and updates about digital business cards, networking, and business growth.",
    type: "website",
    url: "/blog",
  },
  twitter: {
    card: "summary_large_image",
    title: "Blog | DukanCard",
    description: "Discover the latest insights, tips, and updates about digital business cards, networking, and business growth.",
  },
};

interface BlogPageProps {
  searchParams: Promise<{
    page?: string;
    query?: string;
    sort?: 'newest' | 'oldest';
  }>;
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  const params = await searchParams;
  const page = parseInt(params.page || '1', 10);
  const query = params.query || '';
  const sort = (params.sort as 'newest' | 'oldest') || 'newest';

  // Fetch initial data using server action
  const initialData = await searchBlogs({
    query,
    sort,
    page,
  });

  return (
    <Suspense fallback={<BlogListingSkeleton />}>
      <BlogListingClient
        initialBlogs={initialData.blogs}
        initialTotalCount={initialData.total}
        initialPage={page}
        initialQuery={query}
        initialSort={sort}
      />
    </Suspense>
  );
}
