#!/usr/bin/env tsx

/**
 * REAL-WORLD USER JOURNEY TESTING
 * 
 * Simulates complete user journeys from signup to cancellation
 * Tests end-to-end subscription flows that users actually experience
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import { ScenarioTestRunner } from './runners/scenarioTestRunner';
import { SubscriptionScenario } from './scenarios/subscriptionScenarios';

const BUSINESS_ID = 'd4fe2395-3872-4522-9b67-0d280633f318';

interface UserJourney {
  id: string;
  name: string;
  description: string;
  steps: SubscriptionScenario[];
  expectedOutcome: string;
  userType: 'trial_user' | 'paying_customer' | 'enterprise_customer' | 'price_sensitive_user';
}

// Define real-world user journeys
const USER_JOURNEYS: UserJourney[] = [
  {
    id: 'trial_to_paid_success',
    name: 'Trial User → Successful Conversion',
    description: 'New user tries trial, likes the product, converts to paid plan',
    userType: 'trial_user',
    expectedOutcome: 'Active paid subscription',
    steps: [
      {
        id: 'step1_trial_start',
        name: 'User Starts Trial',
        description: 'New user begins trial period',
        initialState: { subscription_status: 'trial', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: false },
        webhookEvent: 'subscription.authenticated',
        expectedFinalState: { subscription_status: 'authenticated', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: false },
        shouldSucceed: true,
        category: 'trial',
        paymentMethod: 'card'
      },
      {
        id: 'step2_trial_conversion',
        name: 'User Converts to Paid',
        description: 'Trial user decides to pay and converts',
        initialState: { subscription_status: 'authenticated', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: false },
        webhookEvent: 'subscription.activated',
        expectedFinalState: { subscription_status: 'active', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: true },
        shouldSucceed: true,
        category: 'transition',
        paymentMethod: 'card'
      }
    ]
  },
  {
    id: 'trial_abandonment',
    name: 'Trial User → Abandonment',
    description: 'User tries trial but doesn\'t convert, trial expires',
    userType: 'trial_user',
    expectedOutcome: 'Free plan user',
    steps: [
      {
        id: 'step1_trial_expiry',
        name: 'Trial Expires',
        description: 'User trial period ends without conversion',
        initialState: { subscription_status: 'trial', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: false },
        webhookEvent: 'subscription.expired',
        expectedFinalState: { subscription_status: 'active', plan_id: 'free', plan_cycle: 'monthly', has_active_subscription: false },
        shouldSucceed: true,
        category: 'trial'
      }
    ]
  },
  {
    id: 'paying_customer_upgrade',
    name: 'Paying Customer → Plan Upgrade',
    description: 'Existing customer upgrades to higher plan',
    userType: 'paying_customer',
    expectedOutcome: 'Upgraded active subscription',
    steps: [
      {
        id: 'step1_plan_upgrade',
        name: 'Customer Upgrades Plan',
        description: 'Active customer upgrades from basic to premium',
        initialState: { subscription_status: 'active', plan_id: 'basic', plan_cycle: 'monthly', has_active_subscription: true },
        webhookEvent: 'subscription.updated',
        expectedFinalState: { subscription_status: 'active', plan_id: 'basic', plan_cycle: 'monthly', has_active_subscription: true },
        shouldSucceed: true,
        category: 'paid',
        paymentMethod: 'card'
      }
    ]
  },
  {
    id: 'payment_failure_recovery',
    name: 'Payment Failure → Recovery',
    description: 'Customer payment fails but they recover and resume service',
    userType: 'paying_customer',
    expectedOutcome: 'Recovered active subscription',
    steps: [
      {
        id: 'step1_payment_failure',
        name: 'Payment Fails',
        description: 'Customer renewal payment fails',
        initialState: { subscription_status: 'active', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: true },
        webhookEvent: 'subscription.halted',
        expectedFinalState: { subscription_status: 'halted', plan_id: 'free', plan_cycle: 'monthly', has_active_subscription: false },
        shouldSucceed: true,
        category: 'paid',
        paymentMethod: 'card'
      },
      {
        id: 'step2_payment_recovery',
        name: 'Customer Recovers Payment',
        description: 'Customer updates payment method and reactivates',
        initialState: { subscription_status: 'halted', plan_id: 'free', plan_cycle: 'monthly', has_active_subscription: false },
        webhookEvent: 'subscription.activated',
        expectedFinalState: { subscription_status: 'active', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: true },
        shouldSucceed: true,
        category: 'transition',
        paymentMethod: 'card'
      }
    ]
  },
  {
    id: 'upi_customer_plan_change',
    name: 'UPI Customer → Plan Change',
    description: 'UPI customer changes plan (requires create-new-cancel-old)',
    userType: 'paying_customer',
    expectedOutcome: 'New subscription with updated plan',
    steps: [
      {
        id: 'step1_new_subscription',
        name: 'New UPI Subscription Created',
        description: 'New subscription created for plan change',
        initialState: { subscription_status: 'active', plan_id: 'basic', plan_cycle: 'monthly', has_active_subscription: true },
        webhookEvent: 'subscription.activated',
        expectedFinalState: { subscription_status: 'active', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: true },
        shouldSucceed: true,
        category: 'paid',
        paymentMethod: 'upi',
        isCreateNewCancelOld: true
      }
    ]
  },
  {
    id: 'enterprise_customer_journey',
    name: 'Enterprise Customer → Full Lifecycle',
    description: 'Enterprise customer from trial to yearly premium to eventual cancellation',
    userType: 'enterprise_customer',
    expectedOutcome: 'Complete enterprise lifecycle',
    steps: [
      {
        id: 'step1_enterprise_trial',
        name: 'Enterprise Trial',
        description: 'Enterprise customer starts with premium trial',
        initialState: { subscription_status: 'trial', plan_id: 'premium', plan_cycle: 'yearly', has_active_subscription: false },
        webhookEvent: 'subscription.activated',
        expectedFinalState: { subscription_status: 'active', plan_id: 'premium', plan_cycle: 'yearly', has_active_subscription: true },
        shouldSucceed: true,
        category: 'trial',
        paymentMethod: 'card'
      },
      {
        id: 'step2_enterprise_renewal',
        name: 'Enterprise Renewal',
        description: 'Enterprise customer renews yearly subscription',
        initialState: { subscription_status: 'active', plan_id: 'premium', plan_cycle: 'yearly', has_active_subscription: true },
        webhookEvent: 'subscription.charged',
        expectedFinalState: { subscription_status: 'active', plan_id: 'premium', plan_cycle: 'yearly', has_active_subscription: true },
        shouldSucceed: true,
        category: 'paid',
        paymentMethod: 'card'
      }
    ]
  },
  {
    id: 'price_sensitive_user_journey',
    name: 'Price-Sensitive User → Cancellation',
    description: 'User tries service but cancels due to price concerns',
    userType: 'price_sensitive_user',
    expectedOutcome: 'Cancelled subscription, back to free',
    steps: [
      {
        id: 'step1_quick_cancellation',
        name: 'User Cancels Quickly',
        description: 'User pays but quickly cancels due to price',
        initialState: { subscription_status: 'active', plan_id: 'growth', plan_cycle: 'monthly', has_active_subscription: true },
        webhookEvent: 'subscription.cancelled',
        expectedFinalState: { subscription_status: 'active', plan_id: 'free', plan_cycle: 'monthly', has_active_subscription: false },
        shouldSucceed: true,
        category: 'paid',
        paymentMethod: 'card'
      }
    ]
  }
];

async function runUserJourneyTests() {
  console.log('🚀 Real-World User Journey Testing');
  console.log('==================================');
  console.log(`📋 Business ID: ${BUSINESS_ID}`);
  console.log(`👥 Total User Journeys: ${USER_JOURNEYS.length}`);
  console.log('');

  const runner = new ScenarioTestRunner(BUSINESS_ID);
  await runner.initialize(); // Initialize the Supabase client
  let totalJourneys = 0;
  let successfulJourneys = 0;
  let totalSteps = 0;
  let successfulSteps = 0;

  for (const journey of USER_JOURNEYS) {
    console.log(`\n👤 ${journey.name} (${journey.userType})`);
    console.log(`   Description: ${journey.description}`);
    console.log(`   Expected Outcome: ${journey.expectedOutcome}`);
    console.log(`   Steps: ${journey.steps.length}`);
    console.log('─'.repeat(60));
    
    totalJourneys++;
    let journeySuccess = true;
    let currentState = journey.steps[0].initialState;

    for (let i = 0; i < journey.steps.length; i++) {
      const step = journey.steps[i];
      totalSteps++;
      
      console.log(`\n   🔄 Step ${i + 1}: ${step.name}`);
      console.log(`      ${step.description}`);
      console.log(`      Current State: ${currentState.subscription_status}/${currentState.plan_id}/${currentState.has_active_subscription}`);
      
      // Update step initial state to current state
      step.initialState = { ...currentState };
      
      try {
        const startTime = Date.now();
        const result = await runner.runScenario(step);
        const duration = (Date.now() - startTime) / 1000;
        
        if (result.success === step.shouldSucceed) {
          console.log(`      ✅ Step completed successfully (${duration.toFixed(2)}s)`);
          successfulSteps++;
          
          // Update current state for next step
          currentState = { ...step.expectedFinalState };
        } else {
          console.log(`      ❌ Step failed (${duration.toFixed(2)}s): ${result.message}`);
          journeySuccess = false;
          break;
        }
        
      } catch (error) {
        console.log(`      💥 Step error: ${error instanceof Error ? error.message : String(error)}`);
        journeySuccess = false;
        break;
      }
      
      // Small delay between steps
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    if (journeySuccess) {
      console.log(`\n   🎉 Journey completed successfully!`);
      console.log(`      Final State: ${currentState.subscription_status}/${currentState.plan_id}/${currentState.has_active_subscription}`);
      successfulJourneys++;
    } else {
      console.log(`\n   ❌ Journey failed or incomplete`);
    }
  }

  // Generate summary report
  console.log('\n' + '='.repeat(60));
  console.log('📊 USER JOURNEY TEST RESULTS');
  console.log('='.repeat(60));
  
  const journeySuccessRate = ((successfulJourneys / totalJourneys) * 100).toFixed(1);
  const stepSuccessRate = ((successfulSteps / totalSteps) * 100).toFixed(1);
  
  console.log(`\n🎯 Overall Results:`);
  console.log(`   Total Journeys: ${totalJourneys}`);
  console.log(`   Successful Journeys: ${successfulJourneys}`);
  console.log(`   Journey Success Rate: ${journeySuccessRate}%`);
  console.log(`   Total Steps: ${totalSteps}`);
  console.log(`   Successful Steps: ${successfulSteps}`);
  console.log(`   Step Success Rate: ${stepSuccessRate}%`);
  
  // User type breakdown
  const userTypeResults = USER_JOURNEYS.reduce((acc, journey) => {
    if (!acc[journey.userType]) {
      acc[journey.userType] = { total: 0, successful: 0 };
    }
    acc[journey.userType].total++;
    // Check if journey was successful (simplified check)
    if (successfulJourneys > 0) acc[journey.userType].successful++;
    return acc;
  }, {} as Record<string, { total: number; successful: number }>);
  
  console.log(`\n👥 User Type Breakdown:`);
  for (const [userType, data] of Object.entries(userTypeResults)) {
    const rate = ((data.successful / data.total) * 100).toFixed(1);
    console.log(`   ${userType}: ${data.successful}/${data.total} (${rate}%)`);
  }
  
  const isUserExperienceGood = parseFloat(journeySuccessRate) >= 90.0;
  
  if (isUserExperienceGood) {
    console.log('\n🎉 EXCELLENT USER EXPERIENCE!');
    console.log('✅ All critical user journeys work smoothly');
    console.log('✅ Real-world scenarios handled properly');
    console.log('✅ Users will have positive experience');
  } else {
    console.log('\n⚠️ USER EXPERIENCE CONCERNS');
    console.log('🔧 Some user journeys are failing');
    console.log('📋 Fix critical user flows before launch');
  }
  
  console.log('='.repeat(60));
  
  process.exit(isUserExperienceGood ? 0 : 1);
}

runUserJourneyTests().catch(console.error);
