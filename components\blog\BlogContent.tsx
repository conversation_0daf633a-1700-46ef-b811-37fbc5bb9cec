"use client";

import { motion } from "framer-motion";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import { cn } from "@/lib/utils";
import { processMarkdownContent } from "@/lib/utils/markdown";
import Image from "next/image";
import Link from "next/link";
import { ExternalLink } from "lucide-react";

// Import highlight.js CSS for syntax highlighting
import "highlight.js/styles/github-dark.css";

interface BlogContentProps {
  content: string;
  className?: string;
}

export default function BlogContent({ content, className }: BlogContentProps) {
  if (!content) {
    return <div>No content available</div>;
  }

  // Process content to handle escaped characters and JSON-escaped strings
  const processedContent = processMarkdownContent(content);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={cn("prose prose-lg prose-neutral dark:prose-invert max-w-none prose-headings:scroll-mt-24", className)}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Paragraphs
          p: ({ children }) => <p className="mb-6 leading-relaxed text-base sm:text-lg">{children}</p>,

          // Headings
          h1: ({ children }) => <h1 className="mt-12 mb-6 text-3xl sm:text-4xl font-bold text-foreground">{children}</h1>,
          h2: ({ children }) => <h2 className="mt-10 mb-5 text-2xl sm:text-3xl font-bold text-foreground">{children}</h2>,
          h3: ({ children }) => <h3 className="mt-8 mb-4 text-xl sm:text-2xl font-bold text-foreground">{children}</h3>,
          h4: ({ children }) => <h4 className="mt-6 mb-3 text-lg sm:text-xl font-bold text-foreground">{children}</h4>,
          h5: ({ children }) => <h5 className="mt-5 mb-2 text-base sm:text-lg font-bold text-foreground">{children}</h5>,
          h6: ({ children }) => <h6 className="mt-4 mb-2 text-sm sm:text-base font-bold text-foreground">{children}</h6>,

          // Lists
          ul: ({ children }) => <ul className="list-disc pl-6 mb-6 space-y-2">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal pl-6 mb-6 space-y-2">{children}</ol>,
          li: ({ children }) => <li className="leading-relaxed text-base sm:text-lg">{children}</li>,

          // Tables
          table: ({ children }) => (
            <table className="w-full border-collapse my-6">{children}</table>
          ),
          th: ({ children }) => (
            <th className="border px-4 py-2 text-left bg-muted font-semibold">{children}</th>
          ),
          td: ({ children }) => <td className="border px-4 py-2">{children}</td>,

          // Link styling with external link icon
          a: ({ href, children, ...props }) => {
            const isExternal = href?.startsWith('http');

            if (isExternal) {
              return (
                <a
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-blue-600 hover:underline"
                  {...props}
                >
                  {children}
                  <ExternalLink className="h-3 w-3" />
                </a>
              );
            }

            return (
              <Link href={href || '#'} className="text-blue-600 hover:underline" {...props}>
                {children}
              </Link>
            );
          },

          // Code styling - only handle inline code, let rehype-highlight handle code blocks
          code: ({ children, className, ...props }) => {
            const isInline = !className?.includes('language-');
            if (isInline) {
              return (
                <code
                  className="bg-muted px-1.5 py-0.5 rounded text-sm font-mono border text-purple-600"
                  {...props}
                >
                  {children}
                </code>
              );
            }

            // For code blocks with language, let rehype-highlight handle it
            return (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },

          // Pre styling for code blocks
          pre: ({ children, ...props }) => (
            <pre
              className="bg-muted p-4 rounded-lg overflow-x-auto my-4 border"
              {...props}
            >
              {children}
            </pre>
          ),

          // Blockquote styling
          blockquote: ({ children, ...props }) => (
            <blockquote
              className="border-l-4 border-l-primary pl-6 pr-4 py-4 my-8 italic bg-muted/30 rounded-r-lg text-foreground/80 text-lg"
              {...props}
            >
              {children}
            </blockquote>
          ),

          // Image styling
          img: ({ src, alt }) => {
            if (!src || typeof src !== 'string') return null;

            return (
              <div className="my-8 rounded-xl overflow-hidden shadow-lg bg-muted/30">
                <Image
                  src={src}
                  alt={alt || ''}
                  width={800}
                  height={400}
                  className="w-full h-auto object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 800px"
                />
                {alt && (
                  <p className="text-sm text-muted-foreground text-center mt-3 px-4 italic">
                    {alt}
                  </p>
                )}
              </div>
            );
          },
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </motion.div>
  );
}
