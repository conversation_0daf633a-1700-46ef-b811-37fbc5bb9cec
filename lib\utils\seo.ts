/**
 * SEO helper functions for meta tags, slugs, and optimization
 */

import type { Blog, BlogMetadata } from '@/lib/types/blog';
import { extractTextFromMarkdown } from './markdown';

/**
 * Generate SEO metadata for a blog post
 */
export function generateBlogMetadata(blog: Blog, baseUrl: string = ''): BlogMetadata {
  const url = `${baseUrl}/blog/${blog.slug}`;
  const description = blog.meta_description || 
    blog.excerpt || 
    extractTextFromMarkdown(blog.content, 160);
  
  return {
    title: blog.meta_title || blog.title,
    description,
    image: blog.featured_image_url,
    url,
    publishedTime: blog.published_at,
    author: blog.author_name,
    tags: blog.tags,
    categories: blog.categories,
  };
}

/**
 * Generate Open Graph meta tags for a blog post
 */
export function generateOpenGraphTags(metadata: BlogMetadata) {
  const tags = [
    { property: 'og:type', content: 'article' },
    { property: 'og:title', content: metadata.title },
    { property: 'og:description', content: metadata.description },
    { property: 'og:url', content: metadata.url },
    { property: 'og:site_name', content: 'DukanCard Blog' },
  ];

  if (metadata.image) {
    tags.push({ property: 'og:image', content: metadata.image });
    tags.push({ property: 'og:image:alt', content: metadata.title });
  }

  if (metadata.publishedTime) {
    tags.push({ property: 'article:published_time', content: metadata.publishedTime });
  }

  if (metadata.author) {
    tags.push({ property: 'article:author', content: metadata.author });
  }

  if (metadata.tags && metadata.tags.length > 0) {
    metadata.tags.forEach(tag => {
      tags.push({ property: 'article:tag', content: tag });
    });
  }

  return tags;
}

/**
 * Generate Twitter Card meta tags for a blog post
 */
export function generateTwitterCardTags(metadata: BlogMetadata) {
  const tags = [
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: metadata.title },
    { name: 'twitter:description', content: metadata.description },
  ];

  if (metadata.image) {
    tags.push({ name: 'twitter:image', content: metadata.image });
    tags.push({ name: 'twitter:image:alt', content: metadata.title });
  }

  return tags;
}

/**
 * Generate JSON-LD structured data for a blog post
 */
export function generateBlogJsonLd(blog: Blog, baseUrl: string = '') {
  const url = `${baseUrl}/blog/${blog.slug}`;
  const imageUrl = blog.featured_image_url;
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: blog.title,
    description: blog.meta_description || blog.excerpt || extractTextFromMarkdown(blog.content, 160),
    image: imageUrl ? [imageUrl] : undefined,
    url,
    datePublished: blog.published_at,
    dateModified: blog.updated_at,
    author: {
      '@type': 'Person',
      name: blog.author_name,
      email: blog.author_email,
    },
    publisher: {
      '@type': 'Organization',
      name: 'DukanCard',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/logo.png`, // Update with actual logo URL
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url,
    },
    keywords: blog.tags?.join(', '),
    articleSection: blog.categories?.join(', '),
  };
}

/**
 * Generate breadcrumb structured data for blog pages
 */
export function generateBlogBreadcrumbJsonLd(blog?: Blog, baseUrl: string = '') {
  const items = [
    {
      '@type': 'ListItem',
      position: 1,
      name: 'Home',
      item: baseUrl || '/',
    },
    {
      '@type': 'ListItem',
      position: 2,
      name: 'Blog',
      item: `${baseUrl}/blog`,
    },
  ];

  if (blog) {
    items.push({
      '@type': 'ListItem',
      position: 3,
      name: blog.title,
      item: `${baseUrl}/blog/${blog.slug}`,
    });
  }

  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items,
  };
}

/**
 * Generate canonical URL for blog pages
 */
export function generateCanonicalUrl(slug?: string, baseUrl: string = ''): string {
  if (slug) {
    return `${baseUrl}/blog/${slug}`;
  }
  return `${baseUrl}/blog`;
}

/**
 * Validate and clean meta description
 */
export function validateMetaDescription(description: string): string {
  if (!description) return '';
  
  // Optimal length: 150-160 characters
  const maxLength = 160;
  let cleaned = description.trim();
  
  if (cleaned.length > maxLength) {
    cleaned = cleaned.substring(0, maxLength - 3).trim() + '...';
  }
  
  return cleaned;
}

/**
 * Validate and clean meta title
 */
export function validateMetaTitle(title: string): string {
  if (!title) return '';
  
  // Optimal length: 50-60 characters
  const maxLength = 60;
  let cleaned = title.trim();
  
  if (cleaned.length > maxLength) {
    cleaned = cleaned.substring(0, maxLength - 3).trim() + '...';
  }
  
  return cleaned;
}

/**
 * Generate sitemap entry for a blog post
 */
export function generateSitemapEntry(blog: Blog, baseUrl: string = '') {
  return {
    url: `${baseUrl}/blog/${blog.slug}`,
    lastModified: blog.updated_at,
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  };
}

/**
 * Extract keywords from blog content for SEO
 */
export function extractKeywords(content: string, maxKeywords: number = 10): string[] {
  if (!content) return [];
  
  const text = extractTextFromMarkdown(content, Infinity).toLowerCase();
  
  // Simple keyword extraction (can be enhanced with NLP libraries)
  const words = text
    .split(/\s+/)
    .filter(word => word.length > 3) // Filter short words
    .filter(word => !/^\d+$/.test(word)) // Filter numbers
    .filter(word => !commonWords.includes(word)); // Filter common words
  
  // Count word frequency
  const wordCount: Record<string, number> = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  // Sort by frequency and return top keywords
  return Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxKeywords)
    .map(([word]) => word);
}

// Common words to filter out from keyword extraction
const commonWords = [
  'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our',
  'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two',
  'who', 'boy', 'did', 'does', 'let', 'put', 'say', 'she', 'too', 'use', 'that', 'with', 'have',
  'this', 'will', 'your', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time',
  'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take',
  'than', 'them', 'well', 'were', 'what', 'would', 'there', 'each', 'which', 'their', 'said',
  'about', 'after', 'again', 'before', 'other', 'right', 'think', 'where', 'being', 'every',
  'first', 'great', 'might', 'shall', 'still', 'those', 'under', 'while', 'these', 'could',
  'should', 'through', 'between', 'another', 'because', 'without', 'around', 'during', 'before',
  'against', 'within', 'across', 'behind', 'beyond', 'inside', 'outside', 'toward', 'beneath'
];
