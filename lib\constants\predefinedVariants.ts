/**
 * Simplified predefined variant types and options for common business needs
 * Focused on essential variants that most businesses actually use
 */

// VariantType interface definition (since it was removed from types/variants.ts)
interface VariantType {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  is_predefined: boolean;
  sort_order: number;
}

export interface PredefinedVariantOption {
  value: string;
  display_value: string;
  color_code?: string;
  description?: string;
  sort_order: number;
}

// ============================================================================
// VARIANT TYPES - Essential variants only
// ============================================================================

export const PREDEFINED_VARIANT_TYPES: Omit<VariantType, 'created_at' | 'updated_at'>[] = [
  // Most Common Variants
  { id: 'size', name: 'size', display_name: 'Size', description: 'Product size variations', is_predefined: true, sort_order: 1 },
  { id: 'color', name: 'color', display_name: 'Color', description: 'Product color variations', is_predefined: true, sort_order: 2 },
  { id: 'material', name: 'material', display_name: 'Material', description: 'Product material', is_predefined: true, sort_order: 3 },
  { id: 'style', name: 'style', display_name: 'Style', description: 'Product style variations', is_predefined: true, sort_order: 4 },
  { id: 'pattern', name: 'pattern', display_name: 'Pattern', description: 'Product pattern or design', is_predefined: true, sort_order: 5 },

  // Technical Specifications
  { id: 'capacity', name: 'capacity', display_name: 'Capacity', description: 'Storage or volume capacity', is_predefined: true, sort_order: 6 },
  { id: 'weight', name: 'weight', display_name: 'Weight', description: 'Product weight variations', is_predefined: true, sort_order: 7 },
  { id: 'power', name: 'power', display_name: 'Power', description: 'Power rating', is_predefined: true, sort_order: 8 },

  // Food & Consumables
  { id: 'flavor', name: 'flavor', display_name: 'Flavor', description: 'Taste or flavor variations', is_predefined: true, sort_order: 9 },
  { id: 'quantity', name: 'quantity', display_name: 'Quantity', description: 'Package quantity or count', is_predefined: true, sort_order: 10 },

  // Beauty & Personal Care
  { id: 'shade', name: 'shade', display_name: 'Shade', description: 'Color shade or tone', is_predefined: true, sort_order: 11 },
  { id: 'skin_type', name: 'skin_type', display_name: 'Skin Type', description: 'Suitable skin type', is_predefined: true, sort_order: 12 },

  // Clothing Specific
  { id: 'fit', name: 'fit', display_name: 'Fit', description: 'Clothing fit type', is_predefined: true, sort_order: 13 },
  { id: 'sleeve', name: 'sleeve', display_name: 'Sleeve', description: 'Sleeve type or length', is_predefined: true, sort_order: 14 },

  // General
  { id: 'type', name: 'type', display_name: 'Type', description: 'Product type or variant', is_predefined: true, sort_order: 15 },
  { id: 'finish', name: 'finish', display_name: 'Finish', description: 'Product surface finish', is_predefined: true, sort_order: 16 },
];

// ============================================================================
// SIZE OPTIONS
// ============================================================================

export const SIZE_OPTIONS: PredefinedVariantOption[] = [
  // Clothing Sizes
  { value: 'xxs', display_value: 'XXS', sort_order: 1 },
  { value: 'xs', display_value: 'XS', sort_order: 2 },
  { value: 's', display_value: 'S', sort_order: 3 },
  { value: 'm', display_value: 'M', sort_order: 4 },
  { value: 'l', display_value: 'L', sort_order: 5 },
  { value: 'xl', display_value: 'XL', sort_order: 6 },
  { value: 'xxl', display_value: 'XXL', sort_order: 7 },
  { value: 'xxxl', display_value: 'XXXL', sort_order: 8 },
  { value: '4xl', display_value: '4XL', sort_order: 9 },
  { value: '5xl', display_value: '5XL', sort_order: 10 },
  
  // Numeric Sizes (Clothing)
  { value: 'size_28', display_value: '28', sort_order: 11 },
  { value: 'size_30', display_value: '30', sort_order: 12 },
  { value: 'size_32', display_value: '32', sort_order: 13 },
  { value: 'size_34', display_value: '34', sort_order: 14 },
  { value: 'size_36', display_value: '36', sort_order: 15 },
  { value: 'size_38', display_value: '38', sort_order: 16 },
  { value: 'size_40', display_value: '40', sort_order: 17 },
  { value: 'size_42', display_value: '42', sort_order: 18 },
  { value: 'size_44', display_value: '44', sort_order: 19 },

  // Shoe Sizes
  { value: 'shoe_6', display_value: 'UK 6', sort_order: 20 },
  { value: 'shoe_7', display_value: 'UK 7', sort_order: 21 },
  { value: 'shoe_8', display_value: 'UK 8', sort_order: 22 },
  { value: 'shoe_9', display_value: 'UK 9', sort_order: 23 },
  { value: 'shoe_10', display_value: 'UK 10', sort_order: 24 },
  { value: 'shoe_11', display_value: 'UK 11', sort_order: 25 },
  { value: 'shoe_12', display_value: 'UK 12', sort_order: 26 },

  // General Sizes
  { value: 'small', display_value: 'Small', sort_order: 27 },
  { value: 'medium', display_value: 'Medium', sort_order: 28 },
  { value: 'large', display_value: 'Large', sort_order: 29 },
  { value: 'extra_large', display_value: 'Extra Large', sort_order: 30 },
  { value: 'one_size', display_value: 'One Size', sort_order: 31 },
  { value: 'free_size', display_value: 'Free Size', sort_order: 32 },
];

// ============================================================================
// COLOR OPTIONS
// ============================================================================

export const COLOR_OPTIONS: PredefinedVariantOption[] = [
  // Basic Colors
  { value: 'black', display_value: 'Black', color_code: '#000000', sort_order: 1 },
  { value: 'white', display_value: 'White', color_code: '#FFFFFF', sort_order: 2 },
  { value: 'gray', display_value: 'Gray', color_code: '#808080', sort_order: 3 },
  { value: 'red', display_value: 'Red', color_code: '#FF0000', sort_order: 4 },
  { value: 'blue', display_value: 'Blue', color_code: '#0000FF', sort_order: 5 },
  { value: 'green', display_value: 'Green', color_code: '#008000', sort_order: 6 },
  { value: 'yellow', display_value: 'Yellow', color_code: '#FFFF00', sort_order: 7 },
  { value: 'orange', display_value: 'Orange', color_code: '#FFA500', sort_order: 8 },
  { value: 'purple', display_value: 'Purple', color_code: '#800080', sort_order: 9 },
  { value: 'pink', display_value: 'Pink', color_code: '#FFC0CB', sort_order: 10 },
  { value: 'brown', display_value: 'Brown', color_code: '#A52A2A', sort_order: 11 },
  { value: 'navy', display_value: 'Navy', color_code: '#000080', sort_order: 12 },
  { value: 'maroon', display_value: 'Maroon', color_code: '#800000', sort_order: 13 },
  { value: 'gold', display_value: 'Gold', color_code: '#FFD700', sort_order: 14 },
  { value: 'silver', display_value: 'Silver', color_code: '#C0C0C0', sort_order: 15 },
  { value: 'multicolor', display_value: 'Multicolor', sort_order: 16 },
];

// ============================================================================
// MATERIAL OPTIONS
// ============================================================================

export const MATERIAL_OPTIONS: PredefinedVariantOption[] = [
  // Common Fabrics
  { value: 'cotton', display_value: 'Cotton', sort_order: 1 },
  { value: 'silk', display_value: 'Silk', sort_order: 2 },
  { value: 'wool', display_value: 'Wool', sort_order: 3 },
  { value: 'polyester', display_value: 'Polyester', sort_order: 4 },
  { value: 'linen', display_value: 'Linen', sort_order: 5 },
  { value: 'denim', display_value: 'Denim', sort_order: 6 },
  { value: 'leather', display_value: 'Leather', sort_order: 7 },
  { value: 'faux_leather', display_value: 'Faux Leather', sort_order: 8 },

  // Common Materials
  { value: 'plastic', display_value: 'Plastic', sort_order: 9 },
  { value: 'metal', display_value: 'Metal', sort_order: 10 },
  { value: 'wood', display_value: 'Wood', sort_order: 11 },
  { value: 'glass', display_value: 'Glass', sort_order: 12 },
  { value: 'ceramic', display_value: 'Ceramic', sort_order: 13 },
  { value: 'rubber', display_value: 'Rubber', sort_order: 14 },
  { value: 'paper', display_value: 'Paper', sort_order: 15 },
  { value: 'bamboo', display_value: 'Bamboo', sort_order: 16 },
];

// ============================================================================
// STYLE OPTIONS
// ============================================================================

export const STYLE_OPTIONS: PredefinedVariantOption[] = [
  { value: 'classic', display_value: 'Classic', sort_order: 1 },
  { value: 'modern', display_value: 'Modern', sort_order: 2 },
  { value: 'vintage', display_value: 'Vintage', sort_order: 3 },
  { value: 'traditional', display_value: 'Traditional', sort_order: 4 },
  { value: 'casual', display_value: 'Casual', sort_order: 5 },
  { value: 'formal', display_value: 'Formal', sort_order: 6 },
  { value: 'sporty', display_value: 'Sporty', sort_order: 7 },
  { value: 'elegant', display_value: 'Elegant', sort_order: 8 },
  { value: 'premium', display_value: 'Premium', sort_order: 9 },
  { value: 'basic', display_value: 'Basic', sort_order: 10 },
];

// ============================================================================
// PATTERN OPTIONS
// ============================================================================

export const PATTERN_OPTIONS: PredefinedVariantOption[] = [
  { value: 'solid', display_value: 'Solid', sort_order: 1 },
  { value: 'striped', display_value: 'Striped', sort_order: 2 },
  { value: 'polka_dot', display_value: 'Polka Dot', sort_order: 3 },
  { value: 'checkered', display_value: 'Checkered', sort_order: 4 },
  { value: 'floral', display_value: 'Floral', sort_order: 5 },
  { value: 'geometric', display_value: 'Geometric', sort_order: 6 },
  { value: 'abstract', display_value: 'Abstract', sort_order: 7 },
  { value: 'printed', display_value: 'Printed', sort_order: 8 },
];

// ============================================================================
// FINISH OPTIONS
// ============================================================================

export const FINISH_OPTIONS: PredefinedVariantOption[] = [
  { value: 'matte', display_value: 'Matte', sort_order: 1 },
  { value: 'glossy', display_value: 'Glossy', sort_order: 2 },
  { value: 'satin', display_value: 'Satin', sort_order: 3 },
  { value: 'textured', display_value: 'Textured', sort_order: 4 },
  { value: 'smooth', display_value: 'Smooth', sort_order: 5 },
  { value: 'polished', display_value: 'Polished', sort_order: 6 },
];

// ============================================================================
// FLAVOR OPTIONS (Food & Beverages)
// ============================================================================

export const FLAVOR_OPTIONS: PredefinedVariantOption[] = [
  // Common Flavors
  { value: 'original', display_value: 'Original', sort_order: 1 },
  { value: 'vanilla', display_value: 'Vanilla', sort_order: 2 },
  { value: 'chocolate', display_value: 'Chocolate', sort_order: 3 },
  { value: 'strawberry', display_value: 'Strawberry', sort_order: 4 },
  { value: 'mango', display_value: 'Mango', sort_order: 5 },
  { value: 'orange', display_value: 'Orange', sort_order: 6 },
  { value: 'lemon', display_value: 'Lemon', sort_order: 7 },
  { value: 'mint', display_value: 'Mint', sort_order: 8 },
  { value: 'spicy', display_value: 'Spicy', sort_order: 9 },
  { value: 'sweet', display_value: 'Sweet', sort_order: 10 },
  { value: 'salty', display_value: 'Salty', sort_order: 11 },
  { value: 'masala', display_value: 'Masala', sort_order: 12 },
];

// ============================================================================
// CAPACITY OPTIONS
// ============================================================================

export const CAPACITY_OPTIONS: PredefinedVariantOption[] = [
  // Volume Capacities
  { value: '100ml', display_value: '100ml', sort_order: 1 },
  { value: '250ml', display_value: '250ml', sort_order: 2 },
  { value: '500ml', display_value: '500ml', sort_order: 3 },
  { value: '1l', display_value: '1L', sort_order: 4 },
  { value: '2l', display_value: '2L', sort_order: 5 },
  { value: '5l', display_value: '5L', sort_order: 6 },

  // Storage Capacities
  { value: '32gb', display_value: '32GB', sort_order: 7 },
  { value: '64gb', display_value: '64GB', sort_order: 8 },
  { value: '128gb', display_value: '128GB', sort_order: 9 },
  { value: '256gb', display_value: '256GB', sort_order: 10 },
  { value: '512gb', display_value: '512GB', sort_order: 11 },
  { value: '1tb', display_value: '1TB', sort_order: 12 },

  // General Capacities
  { value: 'small', display_value: 'Small', sort_order: 13 },
  { value: 'medium', display_value: 'Medium', sort_order: 14 },
  { value: 'large', display_value: 'Large', sort_order: 15 },
  { value: 'extra_large', display_value: 'Extra Large', sort_order: 16 },
];

// ============================================================================
// ADDITIONAL ESSENTIAL OPTIONS
// ============================================================================

// WEIGHT OPTIONS
export const WEIGHT_OPTIONS: PredefinedVariantOption[] = [
  { value: '100g', display_value: '100g', sort_order: 1 },
  { value: '250g', display_value: '250g', sort_order: 2 },
  { value: '500g', display_value: '500g', sort_order: 3 },
  { value: '1kg', display_value: '1kg', sort_order: 4 },
  { value: '2kg', display_value: '2kg', sort_order: 5 },
  { value: '5kg', display_value: '5kg', sort_order: 6 },
  { value: 'light', display_value: 'Light', sort_order: 7 },
  { value: 'medium', display_value: 'Medium', sort_order: 8 },
  { value: 'heavy', display_value: 'Heavy', sort_order: 9 },
];

// POWER OPTIONS
export const POWER_OPTIONS: PredefinedVariantOption[] = [
  { value: '5w', display_value: '5W', sort_order: 1 },
  { value: '10w', display_value: '10W', sort_order: 2 },
  { value: '15w', display_value: '15W', sort_order: 3 },
  { value: '25w', display_value: '25W', sort_order: 4 },
  { value: '40w', display_value: '40W', sort_order: 5 },
  { value: '60w', display_value: '60W', sort_order: 6 },
  { value: '100w', display_value: '100W', sort_order: 7 },
];

// QUANTITY OPTIONS
export const QUANTITY_OPTIONS: PredefinedVariantOption[] = [
  { value: '1_piece', display_value: '1 Piece', sort_order: 1 },
  { value: '2_pieces', display_value: '2 Pieces', sort_order: 2 },
  { value: '3_pieces', display_value: '3 Pieces', sort_order: 3 },
  { value: '5_pieces', display_value: '5 Pieces', sort_order: 4 },
  { value: '10_pieces', display_value: '10 Pieces', sort_order: 5 },
  { value: '12_pieces', display_value: '12 Pieces', sort_order: 6 },
  { value: '24_pieces', display_value: '24 Pieces', sort_order: 7 },
  { value: 'pack_of_6', display_value: 'Pack of 6', sort_order: 8 },
  { value: 'pack_of_12', display_value: 'Pack of 12', sort_order: 9 },
  { value: 'bulk', display_value: 'Bulk', sort_order: 10 },
];

// SHADE OPTIONS (Beauty)
export const SHADE_OPTIONS: PredefinedVariantOption[] = [
  { value: 'fair', display_value: 'Fair', sort_order: 1 },
  { value: 'light', display_value: 'Light', sort_order: 2 },
  { value: 'medium', display_value: 'Medium', sort_order: 3 },
  { value: 'dark', display_value: 'Dark', sort_order: 4 },
  { value: 'deep', display_value: 'Deep', sort_order: 5 },
  { value: 'nude', display_value: 'Nude', sort_order: 6 },
  { value: 'natural', display_value: 'Natural', sort_order: 7 },
];

// SKIN TYPE OPTIONS (Beauty)
export const SKIN_TYPE_OPTIONS: PredefinedVariantOption[] = [
  { value: 'normal', display_value: 'Normal', sort_order: 1 },
  { value: 'dry', display_value: 'Dry', sort_order: 2 },
  { value: 'oily', display_value: 'Oily', sort_order: 3 },
  { value: 'combination', display_value: 'Combination', sort_order: 4 },
  { value: 'sensitive', display_value: 'Sensitive', sort_order: 5 },
  { value: 'all_skin_types', display_value: 'All Skin Types', sort_order: 6 },
];

// FIT OPTIONS (Clothing)
export const FIT_OPTIONS: PredefinedVariantOption[] = [
  { value: 'slim_fit', display_value: 'Slim Fit', sort_order: 1 },
  { value: 'regular_fit', display_value: 'Regular Fit', sort_order: 2 },
  { value: 'loose_fit', display_value: 'Loose Fit', sort_order: 3 },
  { value: 'oversized', display_value: 'Oversized', sort_order: 4 },
  { value: 'tailored', display_value: 'Tailored', sort_order: 5 },
  { value: 'relaxed', display_value: 'Relaxed', sort_order: 6 },
];

// SLEEVE OPTIONS (Clothing)
export const SLEEVE_OPTIONS: PredefinedVariantOption[] = [
  { value: 'sleeveless', display_value: 'Sleeveless', sort_order: 1 },
  { value: 'short_sleeve', display_value: 'Short Sleeve', sort_order: 2 },
  { value: 'three_quarter', display_value: '3/4 Sleeve', sort_order: 3 },
  { value: 'full_sleeve', display_value: 'Full Sleeve', sort_order: 4 },
  { value: 'cap_sleeve', display_value: 'Cap Sleeve', sort_order: 5 },
];

// TYPE OPTIONS (General)
export const TYPE_OPTIONS: PredefinedVariantOption[] = [
  { value: 'standard', display_value: 'Standard', sort_order: 1 },
  { value: 'premium', display_value: 'Premium', sort_order: 2 },
  { value: 'deluxe', display_value: 'Deluxe', sort_order: 3 },
  { value: 'basic', display_value: 'Basic', sort_order: 4 },
  { value: 'professional', display_value: 'Professional', sort_order: 5 },
  { value: 'home', display_value: 'Home', sort_order: 6 },
  { value: 'commercial', display_value: 'Commercial', sort_order: 7 },
];

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get predefined options for a specific variant type
 */
export const getPredefinedOptionsForType = (variantTypeName: string): PredefinedVariantOption[] => {
  switch (variantTypeName.toLowerCase()) {
    case 'size':
      return SIZE_OPTIONS;
    case 'color':
      return COLOR_OPTIONS;
    case 'material':
      return MATERIAL_OPTIONS;
    case 'style':
      return STYLE_OPTIONS;
    case 'pattern':
      return PATTERN_OPTIONS;
    case 'finish':
      return FINISH_OPTIONS;
    case 'flavor':
      return FLAVOR_OPTIONS;
    case 'capacity':
      return CAPACITY_OPTIONS;
    case 'weight':
      return WEIGHT_OPTIONS;
    case 'power':
      return POWER_OPTIONS;
    case 'quantity':
      return QUANTITY_OPTIONS;
    case 'shade':
      return SHADE_OPTIONS;
    case 'skin_type':
      return SKIN_TYPE_OPTIONS;
    case 'fit':
      return FIT_OPTIONS;
    case 'sleeve':
      return SLEEVE_OPTIONS;
    case 'type':
      return TYPE_OPTIONS;
    default:
      return [];
  }
};

/**
 * Get all available variant types
 */
export const getAllVariantTypes = (): Omit<VariantType, 'created_at' | 'updated_at'>[] => {
  return PREDEFINED_VARIANT_TYPES;
};

/**
 * Check if a variant type has predefined options
 */
export const hasPredefineOptions = (variantTypeName: string): boolean => {
  return getPredefinedOptionsForType(variantTypeName).length > 0;
};

/**
 * Search variant options by query string
 */
export const searchVariantOptions = (variantTypeName: string, query: string): PredefinedVariantOption[] => {
  const options = getPredefinedOptionsForType(variantTypeName);
  if (!query) return options;

  const lowerQuery = query.toLowerCase();
  return options.filter(option =>
    option.display_value.toLowerCase().includes(lowerQuery) ||
    option.value.toLowerCase().includes(lowerQuery)
  );
};

/**
 * Get variant type by name
 */
export const getVariantTypeByName = (name: string): Omit<VariantType, 'created_at' | 'updated_at'> | null => {
  return PREDEFINED_VARIANT_TYPES.find(type => type.name === name.toLowerCase()) || null;
};
