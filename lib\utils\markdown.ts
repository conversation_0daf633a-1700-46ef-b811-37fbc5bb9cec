/**
 * Markdown parsing and rendering utilities for blog content
 */

import { marked } from 'marked';

/**
 * Process markdown content to handle escaped characters and JSON-escaped strings
 * This fixes issues where content is stored with literal \n instead of actual newlines
 */
export function processMarkdownContent(content: string): string {
  if (!content) return '';

  try {
    // First, try to parse as JSON string in case it's double-escaped
    if (content.startsWith('"') && content.endsWith('"')) {
      return JSON.parse(content);
    }

    // Otherwise, manually replace escaped characters
    return content
      .replace(/\\n/g, '\n')  // Replace literal \n with actual newlines
      .replace(/\\t/g, '\t')  // Replace literal \t with actual tabs
      .replace(/\\r/g, '\r')  // Replace literal \r with actual carriage returns
      .replace(/\\\\/g, '\\') // Replace escaped backslashes
      .replace(/\\"/g, '"')   // Replace escaped quotes
      .replace(/\\'/g, "'");  // Replace escaped single quotes
  } catch (_error) {
    // If JSON parsing fails, fall back to manual replacement
    return content
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\r/g, '\r')
      .replace(/\\\\/g, '\\')
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'");
  }
}

// Configure marked options
marked.setOptions({
  gfm: true, // GitHub Flavored Markdown
  breaks: true, // Convert line breaks to <br>
});

/**
 * Parse markdown content to HTML
 */
export async function parseMarkdown(content: string): Promise<string> {
  if (!content) return '';

  try {
    const result = await marked(content);
    return typeof result === 'string' ? result : content;
  } catch (error) {
    console.error('Error parsing markdown:', error);
    return content; // Return original content if parsing fails
  }
}

/**
 * Extract plain text from markdown content (for excerpts)
 */
export function extractTextFromMarkdown(content: string, maxLength: number = 160): string {
  if (!content) return '';
  
  try {
    // Remove markdown syntax
    let text = content
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/`(.*?)`/g, '$1') // Remove inline code
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
      .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
      .replace(/>\s+/g, '') // Remove blockquotes
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    // Truncate to max length
    if (text.length > maxLength) {
      text = text.substring(0, maxLength).trim();
      // Try to break at word boundary
      const lastSpace = text.lastIndexOf(' ');
      if (lastSpace > maxLength * 0.8) {
        text = text.substring(0, lastSpace);
      }
      text += '...';
    }
    
    return text;
  } catch (error) {
    console.error('Error extracting text from markdown:', error);
    return content.substring(0, maxLength) + (content.length > maxLength ? '...' : '');
  }
}

/**
 * Calculate estimated reading time for content
 */
export function calculateReadingTime(content: string): number {
  if (!content) return 0;
  
  // Extract plain text
  const text = extractTextFromMarkdown(content, Infinity);
  
  // Average reading speed: 200 words per minute
  const wordsPerMinute = 200;
  const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
  
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return Math.max(1, minutes); // Minimum 1 minute
}

/**
 * Generate a URL-friendly slug from title
 */
export function generateSlug(title: string): string {
  if (!title) return '';
  
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Validate if slug is unique (to be used with database check)
 */
export function isValidSlug(slug: string): boolean {
  if (!slug) return false;
  
  // Check format: only lowercase letters, numbers, and hyphens
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 100;
}

/**
 * Extract headings from markdown content for table of contents
 */
export function extractHeadings(content: string): Array<{ level: number; text: string; id: string }> {
  if (!content) return [];
  
  const headings: Array<{ level: number; text: string; id: string }> = [];
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  let match;
  
  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    const id = generateSlug(text);
    
    headings.push({ level, text, id });
  }
  
  return headings;
}

/**
 * Add IDs to headings in markdown content
 */
export function addHeadingIds(content: string): string {
  if (!content) return '';
  
  return content.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, text) => {
    const id = generateSlug(text.trim());
    return `${hashes} ${text.trim()} {#${id}}`;
  });
}

/**
 * Sanitize markdown content (basic security)
 */
export function sanitizeMarkdown(content: string): string {
  if (!content) return '';
  
  // Remove potentially dangerous HTML tags and attributes
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
}
