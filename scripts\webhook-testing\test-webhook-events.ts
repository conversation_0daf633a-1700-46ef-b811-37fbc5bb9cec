#!/usr/bin/env tsx

/**
 * WEBHOOK EVENTS COVERAGE TEST
 * 
 * Tests all webhook events to ensure handlers exist and work correctly
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import { ALL_WEBHOOK_EVENTS, generateWebhookPayload } from './events/webhookEvents';

const BUSINESS_ID = 'd4fe2395-3872-4522-9b67-0d280633f318';

interface WebhookTestResult {
  eventType: string;
  handlerExists: boolean;
  handlerWorks: boolean;
  error?: string;
  duration: number;
}

async function testWebhookEventCoverage() {
  console.log('🎯 Webhook Events Coverage Test');
  console.log('===============================');
  console.log(`📋 Business ID: ${BUSINESS_ID}\n`);

  const { createAdminClient } = await import('../../utils/supabase/admin');
  const adminClient = createAdminClient();

  const results: WebhookTestResult[] = [];
  
  // Test subscription events only (most critical)
  const subscriptionEvents = ALL_WEBHOOK_EVENTS.filter(e => e.category === 'subscription');
  
  console.log(`📊 Testing ${subscriptionEvents.length} subscription webhook events\n`);

  for (const event of subscriptionEvents) {
    const startTime = Date.now();
    console.log(`🔍 Testing: ${event.eventType}`);
    
    try {
      // 1. Check if handler exists
      const handler = await getWebhookHandler(event.eventType);
      const handlerExists = handler !== null;
      
      if (!handlerExists) {
        results.push({
          eventType: event.eventType,
          handlerExists: false,
          handlerWorks: false,
          error: 'Handler not found',
          duration: (Date.now() - startTime) / 1000
        });
        console.log(`   ❌ Handler not found`);
        continue;
      }
      
      // 2. Setup test state
      await setupTestState(adminClient, event.eventType);
      
      // 3. Test handler
      const payload = generateWebhookPayload(
        event.eventType,
        `sub_test_${event.eventType.replace('.', '_')}_${Date.now()}`,
        BUSINESS_ID
      );
      
      const eventId = `test_${event.eventType.replace('.', '_')}_${Date.now()}`;
      const result = await (handler as any)(payload, adminClient, eventId);
      
      const handlerWorks = result && typeof result.success === 'boolean';
      
      results.push({
        eventType: event.eventType,
        handlerExists: true,
        handlerWorks,
        error: handlerWorks ? undefined : 'Handler returned invalid result',
        duration: (Date.now() - startTime) / 1000
      });
      
      if (handlerWorks) {
        console.log(`   ✅ Handler works (${result.success ? 'success' : 'handled'})`);
      } else {
        console.log(`   ⚠️ Handler exists but returned invalid result`);
      }
      
    } catch (error) {
      results.push({
        eventType: event.eventType,
        handlerExists: true,
        handlerWorks: false,
        error: error instanceof Error ? error.message : String(error),
        duration: (Date.now() - startTime) / 1000
      });
      console.log(`   ❌ Handler error: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Generate report
  generateCoverageReport(results);
  
  const allWorking = results.every(r => r.handlerExists && r.handlerWorks);
  process.exit(allWorking ? 0 : 1);
}

async function getWebhookHandler(eventType: string): Promise<unknown | null> {
  try {
    switch (eventType) {
      case 'subscription.authenticated':
        const { handleSubscriptionAuthenticated } = await import('../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
        return handleSubscriptionAuthenticated;
        
      case 'subscription.activated':
        const { handleSubscriptionActivated } = await import('../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
        return handleSubscriptionActivated;
        
      case 'subscription.charged':
        const { handleSubscriptionCharged } = await import('../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
        return handleSubscriptionCharged;
        
      case 'subscription.pending':
        const { handleSubscriptionPending } = await import('../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
        return handleSubscriptionPending;
        
      case 'subscription.halted':
        const { handleSubscriptionHalted } = await import('../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionHalted');
        return handleSubscriptionHalted;
        
      case 'subscription.cancelled':
        const { handleSubscriptionCancelled } = await import('../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCancelled');
        return handleSubscriptionCancelled;
        
      case 'subscription.completed':
        const { handleSubscriptionCompleted } = await import('../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCompleted');
        return handleSubscriptionCompleted;
        
      case 'subscription.expired':
        const { handleSubscriptionExpired } = await import('../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionExpired');
        return handleSubscriptionExpired;
        
      case 'subscription.updated':
        const { handleSubscriptionUpdated } = await import('../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionUpdated');
        return handleSubscriptionUpdated;
        
      default:
        return null;
    }
  } catch (error) {
    console.error(`Error loading handler for ${eventType}:`, error);
    return null;
  }
}

async function setupTestState(adminClient: any, eventType: string) {
  // Setup appropriate initial state based on event type
  const initialStatus = getInitialStatusForEvent(eventType);
  
  await adminClient
    .from('payment_subscriptions')
    .update({
      subscription_status: initialStatus,
      plan_id: 'growth',
      plan_cycle: 'monthly',
      updated_at: new Date().toISOString()
    })
    .eq('business_profile_id', BUSINESS_ID);

  await adminClient
    .from('business_profiles')
    .update({
      has_active_subscription: initialStatus === 'active',
      updated_at: new Date().toISOString()
    })
    .eq('id', BUSINESS_ID);
}

function getInitialStatusForEvent(eventType: string): string {
  const eventToInitialStatus: Record<string, string> = {
    'subscription.authenticated': 'trial',
    'subscription.activated': 'authenticated',
    'subscription.charged': 'active',
    'subscription.pending': 'trial',
    'subscription.halted': 'active',
    'subscription.cancelled': 'active',
    'subscription.completed': 'active',
    'subscription.expired': 'active',
    'subscription.updated': 'active'
  };

  return eventToInitialStatus[eventType] || 'trial';
}

function generateCoverageReport(results: WebhookTestResult[]) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 WEBHOOK EVENTS COVERAGE REPORT');
  console.log('='.repeat(60));
  
  const total = results.length;
  const handlersExist = results.filter(r => r.handlerExists).length;
  const handlersWork = results.filter(r => r.handlerExists && r.handlerWorks).length;
  
  console.log(`📈 Coverage Summary:`);
  console.log(`   Total Events: ${total}`);
  console.log(`   Handlers Exist: ${handlersExist}/${total} (${((handlersExist/total)*100).toFixed(1)}%)`);
  console.log(`   Handlers Work: ${handlersWork}/${total} (${((handlersWork/total)*100).toFixed(1)}%)`);
  
  console.log(`\n📋 Event Details:`);
  results.forEach(result => {
    const status = result.handlerExists && result.handlerWorks ? '✅' : '❌';
    console.log(`   ${status} ${result.eventType}`);
    if (result.error) {
      console.log(`      Error: ${result.error}`);
    }
  });
  
  const missingHandlers = results.filter(r => !r.handlerExists);
  if (missingHandlers.length > 0) {
    console.log(`\n❌ Missing Handlers (${missingHandlers.length}):`);
    missingHandlers.forEach(result => {
      console.log(`   • ${result.eventType}`);
    });
  }
  
  const brokenHandlers = results.filter(r => r.handlerExists && !r.handlerWorks);
  if (brokenHandlers.length > 0) {
    console.log(`\n⚠️ Broken Handlers (${brokenHandlers.length}):`);
    brokenHandlers.forEach(result => {
      console.log(`   • ${result.eventType}: ${result.error}`);
    });
  }
  
  console.log('='.repeat(60));
  
  if (handlersWork === total) {
    console.log('🎉 ALL WEBHOOK HANDLERS WORKING!');
    console.log('✅ Complete webhook event coverage');
    console.log('✅ All handlers exist and function correctly');
  } else {
    console.log('⚠️ Some webhook handlers need attention');
    console.log('🔧 Fix missing or broken handlers before production');
  }
  
  console.log('='.repeat(60));
}

testWebhookEventCoverage().catch(console.error);
