"use client";

import { useState } from "react";
import { toast } from "sonner";
import { compressImageUltraAggressiveClient } from "@/lib/utils/client-image-compression";

export type ProductImageUploadStatus = "idle" | "uploading" | "loading" | "success" | "error";

export interface ProductImage {
  id: string;
  file: File | null;
  previewUrl: string;
  isUploading: boolean;
  isNew: boolean;
  originalIndex?: number; // Track the original index from initialImageUrls
}

interface UseProductMultiImageUploadOptions {
  initialImageUrls?: string[] | null;
  initialFeaturedIndex?: number;
  maxImages?: number;
}

export function useProductMultiImageUpload({
  initialImageUrls = null,
  initialFeaturedIndex = 0,
  maxImages = 5,
}: UseProductMultiImageUploadOptions = {}) {
  const [imageUploadStatus, setImageUploadStatus] = useState<ProductImageUploadStatus>("idle");
  const [imageUploadError, setImageUploadError] = useState<string | null>(null);

  // Track removed original image indices
  const [removedOriginalIndices, setRemovedOriginalIndices] = useState<number[]>([]);

  // Initialize images from initial URLs if provided, or create a featured slot if no images
  const initialImages: ProductImage[] = initialImageUrls?.map((url, index) => ({
    id: `existing-${index}`,
    file: null,
    previewUrl: url,
    isUploading: false,
    isNew: false,
    originalIndex: index, // Store the original index from initialImageUrls
  })) || [
    // Create a default featured image slot when no initial images
    {
      id: `featured-${Date.now()}`,
      file: null,
      previewUrl: '',
      isUploading: false,
      isNew: true
    }
  ];

  const [images, setImages] = useState<ProductImage[]>(initialImages);
  const [featuredImageIndex, setFeaturedImageIndex] = useState<number>(
    initialFeaturedIndex < initialImages.length ? initialFeaturedIndex : 0
  );

  const [imageToCrop, setImageToCrop] = useState<{
    dataUrl: string;
    originalFile: File;
    targetIndex: number;
  } | null>(null);

  // Camera functionality has been removed

  // Client-side image optimization
  // Removed unused function

  // Add a new image slot
  const addImageSlot = () => {
    if (images.length >= maxImages) {
      toast.error(`Maximum of ${maxImages} images allowed`);
      return;
    }

    setImages(prev => [
      ...prev,
      {
        id: `new-${Date.now()}`,
        file: null,
        previewUrl: '',
        isUploading: false,
        isNew: true
      }
    ]);
  };

  // Remove an image
  const removeImage = (index: number) => {
    setImages(prev => {
      const newImages = [...prev];

      // Track removed original indices for existing images
      const imageToRemove = newImages[index];
      if (imageToRemove?.originalIndex !== undefined) {
        setRemovedOriginalIndices(prevRemoved => [...prevRemoved, imageToRemove.originalIndex!]);
      }

      // Don't allow removing the last image slot - always keep at least one
      if (newImages.length <= 1) {
        // If it's the only image and has content, just clear it
        if (newImages[0]?.previewUrl) {
          if (newImages[0].previewUrl && newImages[0].isNew) {
            URL.revokeObjectURL(newImages[0].previewUrl);
          }
          newImages[0] = {
            id: `featured-${Date.now()}`,
            file: null,
            previewUrl: '',
            isUploading: false,
            isNew: true
          };
          setFeaturedImageIndex(0);
        }
        return newImages;
      }

      // If removing the featured image, reset featured index
      if (index === featuredImageIndex) {
        if (newImages.length > 1) {
          // Set the first remaining image as featured
          if (index === 0) {
            setFeaturedImageIndex(0); // The next image will become index 0
          } else {
            setFeaturedImageIndex(0); // Set the first image as featured
          }
        }
      }
      // If removing an image before the featured image, adjust the index
      else if (index < featuredImageIndex) {
        setFeaturedImageIndex(featuredImageIndex - 1);
      }

      // If the image has a preview URL, revoke it
      if (newImages[index].previewUrl && newImages[index].isNew) {
        URL.revokeObjectURL(newImages[index].previewUrl);
      }

      newImages.splice(index, 1);
      return newImages;
    });
  };

  // Set an image as featured
  const setAsFeatured = (index: number) => {
    if (index >= 0 && index < images.length) {
      setFeaturedImageIndex(index);
    }
  };

  // Handle file selection for a specific image slot
  const handleFileSelect = async (file: File, index: number) => {
    try {
      // Define allowed image types
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

      // Verify it's an allowed image file type
      if (!allowedTypes.includes(file.type)) {
        toast.error("Please select a valid image file (JPG, PNG, WebP, or GIF).");
        setImageUploadStatus("error");
        setImageUploadError("Please select a valid image file (JPG, PNG, WebP, or GIF).");
        return;
      }

      // Check file size limit (15MB)
      if (file.size > 15 * 1024 * 1024) {
        toast.error("Image too large", {
          description: "Please select an image smaller than 15MB"
        });
        setImageUploadStatus("error");
        setImageUploadError("Image is too large. Please select a smaller image (max 15MB).");
        return;
      }

      // Send to crop
      setImageToCrop({
        dataUrl: URL.createObjectURL(file),
        originalFile: file,
        targetIndex: index
      });
    } catch (error) {
      console.error("Error processing image:", error);
      toast.error("Failed to process image. Please try again.");
      setImageUploadStatus("error");
      setImageUploadError("Failed to process image. Please try again.");
    }
  };

  // Handle crop completion
  const handleCropComplete = async (croppedBlob: Blob | null) => {
    if (!imageToCrop) {
      setImageToCrop(null);
      return;
    }

    const { targetIndex, originalFile } = imageToCrop;
    setImageToCrop(null);

    if (croppedBlob) {
      try {
        // Convert blob to file for compression
        const fileName = originalFile?.name || `product-image-${Date.now()}.jpg`;
        const croppedFile = new File([croppedBlob], fileName, {
          type: 'image/png', // Canvas outputs PNG
          lastModified: Date.now()
        });

        // Compress the image on client-side
        const compressionResult = await compressImageUltraAggressiveClient(croppedFile, {
          maxDimension: 800,
          targetSizeKB: 100
        });

        // Convert compressed blob back to file
        const compressedFile = new File([compressionResult.blob], fileName, {
          type: compressionResult.blob.type
        });

        // Create a preview URL
        const previewUrl = URL.createObjectURL(compressedFile);

        // Update the image at the target index
        setImages(prev => {
          const newImages = [...prev];

          // If there was a previous preview URL for this image, revoke it
          if (newImages[targetIndex]?.previewUrl && newImages[targetIndex].isNew) {
            URL.revokeObjectURL(newImages[targetIndex].previewUrl);
          }

          newImages[targetIndex] = {
            ...newImages[targetIndex],
            file: compressedFile,
            previewUrl,
            isNew: true
          };

          return newImages;
        });

        setImageUploadStatus("success");
      } catch (error) {
        console.error("Error processing cropped image:", error);
        toast.error("Failed to process cropped image");
        setImageUploadStatus("error");
        setImageUploadError("Failed to process cropped image");
      }
    }
  };

  // Handle crop dialog close
  const handleCropDialogClose = () => {
    if (imageToCrop?.dataUrl) {
      URL.revokeObjectURL(imageToCrop.dataUrl);
    }
    setImageToCrop(null);
  };

  // Camera functionality has been removed

  // Image error display component
  const imageErrorDisplay = imageUploadStatus === "error" && imageUploadError ? imageUploadError : null;

  return {
    images,
    featuredImageIndex,
    removedOriginalIndices,
    imageToCrop,
    imageUploadStatus,
    imageUploadError,
    imageErrorDisplay,
    addImageSlot,
    removeImage,
    setAsFeatured,
    handleFileSelect,
    handleCropComplete,
    handleCropDialogClose
  };
}
