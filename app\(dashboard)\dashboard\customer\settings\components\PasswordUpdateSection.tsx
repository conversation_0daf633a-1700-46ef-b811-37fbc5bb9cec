"use client";

import { useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2, KeyRound, Info } from "lucide-react";
import { updateCustomerPassword } from "../actions";
import { Button } from "@/components/ui/button";
import { PasswordComplexitySchema } from "@/lib/schemas/authSchemas";

// Password schema
const PasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: PasswordComplexitySchema,
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

interface PasswordUpdateSectionProps {
  registrationType: 'google' | 'email' | 'phone';
}

export default function PasswordUpdateSection({
  registrationType,
}: PasswordUpdateSectionProps) {
  const isGoogleLogin = registrationType === 'google';
  const [isPending, startTransition] = useTransition();

  // Password Form
  const passwordForm = useForm<z.infer<typeof PasswordSchema>>({
    resolver: zodResolver(PasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Handle password update
  const onPasswordSubmit = (data: z.infer<typeof PasswordSchema>) => {
    startTransition(async () => {
      try {
        // Create FormData object for the server action
        const formData = new FormData();
        formData.append('currentPassword', data.currentPassword);
        formData.append('newPassword', data.newPassword);

        // Call the server action with the required parameters
        const result = await updateCustomerPassword(
          { message: null, success: false }, // Initial state
          formData
        );

        if (result.success) {
          toast.success("Password updated successfully!");
          passwordForm.reset();
        } else {
          toast.error(result.message || "Failed to update password");

          // Set server-side field errors into react-hook-form state
          if (result.errors?.currentPassword) {
            passwordForm.setError('currentPassword', {
              type: 'server',
              message: result.errors.currentPassword.join(', ')
            });
          }

          if (result.errors?.newPassword) {
            passwordForm.setError('newPassword', {
              type: 'server',
              message: result.errors.newPassword.join(', ')
            });
          }
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.1 }}
      className="rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 transition-all duration-300 hover:shadow-lg"
    >
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800">
        <div className="p-2 rounded-lg bg-primary/10 text-primary self-start">
          <KeyRound className="w-4 sm:w-5 h-4 sm:h-5" />
        </div>
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100">
            Password
          </h3>
          <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
            Update your password
          </p>
        </div>
      </div>

      {isGoogleLogin ? (
        <div className="p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg border border-neutral-200 dark:border-neutral-700">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-amber-500" />
            <p className="text-sm text-neutral-700 dark:text-neutral-300">
              You signed up with Google. Password management is handled by your Google account.
            </p>
          </div>
        </div>
      ) : (
        <Form {...passwordForm}>
          <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}>
            <div className="space-y-4">
              <FormField
                control={passwordForm.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                      Current Password
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        {...field}
                        disabled={isPending}
                        className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-red-500" />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                      New Password
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        {...field}
                        disabled={isPending}
                        className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                      />
                    </FormControl>
                    <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
                      Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol
                    </FormDescription>
                    <FormMessage className="text-xs text-red-500" />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                      Confirm Password
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        {...field}
                        disabled={isPending}
                        className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-red-500" />
                  </FormItem>
                )}
              />
              <div className="mt-4 sm:mt-6 flex justify-end">
                <div className="relative group">
                  {/* Border glow effect - matches button size */}
                  <div
                    className="absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300"
                    style={{
                      boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`
                    }}
                  />

                  {/* Strong decorative colored glow elements - positioned relative to button */}
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none" />
                  <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none" />

                  <Button
                    type="submit"
                    disabled={isPending}
                    variant="outline"
                    size="sm"
                    className={`
                      relative overflow-hidden rounded-xl p-3
                      bg-white dark:bg-black
                      border border-purple-200/50 dark:border-purple-700/50
                      shadow-purple-500/40 shadow-lg
                      hover:shadow-xl hover:shadow-purple-500/40
                      transition-all duration-300
                      text-purple-500 dark:text-purple-400
                      hover:bg-purple-500/5 dark:hover:bg-purple-500/10
                      text-xs sm:text-sm h-auto
                      ${isPending ? 'cursor-not-allowed opacity-80' : ''}
                    `}
                  >
                    {isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Change Password
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Form>
      )}
    </motion.div>
  );
}
